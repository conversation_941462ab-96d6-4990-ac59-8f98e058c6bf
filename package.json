{"name": "vscode-tomcat-manager", "displayName": "Tomcat Manager", "description": "VSCode extension for managing multiple Tomcat instances with hot deployment", "version": "1.0.0", "publisher": "local-dev", "engines": {"vscode": "^1.74.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/vscode-tomcat-manager.git"}, "bugs": {"url": "https://github.com/your-username/vscode-tomcat-manager/issues"}, "homepage": "https://github.com/your-username/vscode-tomcat-manager#readme", "license": "MIT", "categories": ["Other"], "keywords": ["tomcat", "java", "web", "deployment", "hot-reload"], "activationEvents": ["onCommand:tomcatManager.addTomcatInstance", "onView:tomcatExplorer"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "tomcatManager.addTomcatInstance", "title": "Add Tomcat Instance", "icon": "$(add)"}, {"command": "tomcatManager.startInstance", "title": "Start", "icon": "$(play)"}, {"command": "tomcatManager.stopInstance", "title": "Stop", "icon": "$(stop)"}, {"command": "tomcatManager.restartInstance", "title": "<PERSON><PERSON>", "icon": "$(refresh)"}, {"command": "tomcatManager.deleteInstance", "title": "Delete", "icon": "$(trash)"}, {"command": "tomcatManager.deployProject", "title": "Deploy Project", "icon": "$(cloud-upload)"}, {"command": "tomcatManager.openInBrowser", "title": "Open in Browser", "icon": "$(globe)"}, {"command": "tomcatManager.showLogs", "title": "Show Logs", "icon": "$(output)"}, {"command": "tomcatManager.configureInstance", "title": "Configure", "icon": "$(gear)"}, {"command": "tomcatManager.refresh", "title": "Refresh", "icon": "$(refresh)"}, {"command": "tomcatManager.openSettings", "title": "Open Settings", "icon": "$(settings-gear)"}, {"command": "tomcatManager.testConfigureInstance", "title": "Test Configure Instance"}], "views": {"explorer": [{"id": "tomcatExplorer", "name": "Tomcat Manager", "when": "true"}]}, "menus": {"view/title": [{"command": "tomcatManager.addTomcatInstance", "when": "view == tomcatExplorer", "group": "navigation"}, {"command": "tomcatManager.refresh", "when": "view == tomcatExplorer", "group": "navigation"}, {"command": "tomcatManager.openSettings", "when": "view == tomcatExplorer", "group": "navigation"}], "view/item/context": [{"command": "tomcatManager.startInstance", "when": "view == tomcatExplorer && viewItem == tomcatInstance-stopped", "group": "inline"}, {"command": "tomcatManager.stopInstance", "when": "view == tomcatExplorer && viewItem == tomcatInstance-running", "group": "inline"}, {"command": "tomcatManager.restartInstance", "when": "view == tomcatExplorer && viewItem == tomcatInstance-running", "group": "inline"}, {"command": "tomcatManager.configureInstance", "when": "view == tomcatExplorer && viewItem =~ /tomcatInstance/", "group": "inline"}, {"command": "tomcatManager.deployProject", "when": "view == tomcatExplorer && viewItem =~ /tomcatInstance/", "group": "deployment"}, {"command": "tomcatManager.openInBrowser", "when": "view == tomcatExplorer && viewItem =~ /tomcatInstance-running|deployedApp/", "group": "browser"}, {"command": "tomcatManager.showLogs", "when": "view == tomcatExplorer && viewItem =~ /tomcatInstance/", "group": "logs"}, {"command": "tomcatManager.deleteInstance", "when": "view == tomcatExplorer && viewItem =~ /tomcatInstance/", "group": "delete"}]}, "configuration": {"title": "Tomcat Manager", "properties": {"tomcatManager.baseTomcatPath": {"type": "string", "default": "", "description": "Path to the base Tomcat installation"}, "tomcatManager.defaultJrePath": {"type": "string", "default": "", "description": "Default JRE path for Tom<PERSON> instances"}, "tomcatManager.defaultBrowser": {"type": "string", "enum": ["default", "chrome", "firefox", "safari", "edge", "custom"], "default": "default", "description": "Default browser for opening applications"}, "tomcatManager.customBrowserPath": {"type": "string", "default": "", "description": "Path to custom browser executable"}, "tomcatManager.autoOpenBrowser": {"type": "boolean", "default": true, "description": "Automatically open browser when starting Tom<PERSON>"}, "tomcatManager.portRange": {"type": "object", "properties": {"httpStart": {"type": "number", "default": 8080}, "httpsStart": {"type": "number", "default": 8443}, "ajpStart": {"type": "number", "default": 8009}, "jmxStart": {"type": "number", "default": 9999}, "shutdownStart": {"type": "number", "default": 8005}}, "default": {"httpStart": 8080, "httpsStart": 8443, "ajpStart": 8009, "jmxStart": 9999, "shutdownStart": 8005}, "description": "Default port ranges for new instances"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/mocha": "^10.0.10", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0", "eslint": "^8.28.0", "glob": "^11.0.3", "mocha": "^11.7.1", "typescript": "^4.9.4"}, "dependencies": {"@types/yauzl": "^2.10.3", "adm-zip": "^0.5.16", "portfinder": "^1.0.32", "tree-kill": "^1.2.2", "xml2js": "^0.4.23", "yauzl": "^3.2.0"}}