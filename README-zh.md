# 🚀 VSCode Tomcat Manager 扩展

一个功能全面的VSCode扩展，用于管理Apache Tomcat服务器，具有热部署、项目管理和图形化配置等高级功能。

## ✨ 功能特性

### 🚀 核心功能
- 🔧 **多Tomcat实例管理** - 创建和管理多个Tomcat服务器实例
- 🚀 **智能热部署** - 文件更改时自动部署，支持智能文件类型检测
- 📦 **项目管理** - 扫描和部署Maven/Gradle Web项目，支持多模块项目
- 🎨 **图形化界面** - 用户友好的Web配置面板
- 🔍 **端口管理** - 自动端口检测和冲突解决
- 📊 **实时监控** - 实时状态更新和日志查看
- 🌐 **浏览器集成** - 自动浏览器启动和自定义设置

### 🐛 调试功能
- 🐛 **一键调试** - 独立的调试按钮，轻松进入调试模式
- 🔧 **自动调试配置** - 自动创建VSCode的launch.json配置
- 🎯 **Java版本检测** - 智能JDWP参数生成，支持不同Java版本
- 🔄 **旧实例支持** - 自动升级现有实例以支持调试功能

### 🏗️ 构建优化
- ⚡ **大型项目优化** - 针对多模块项目(10+模块)的智能构建策略
- 📦 **增量构建** - WAR文件最新时跳过不必要的重建
- 🔄 **并行编译** - 多线程构建，加快部署速度
- 🎯 **Maven多模块支持** - 智能依赖解析，无需手动install

### 📋 增强日志
- 🎨 **智能日志分类** - 区分错误和正常操作
- 📊 **详细启动信息** - 全面的实例状态报告
- 🔍 **调试信息显示** - 清晰的调试端口和连接详情

## 🚀 快速开始

1. **安装扩展**
   - 在VSCode扩展中搜索 "Tomcat Manager"
   - 或从 [VSCode市场](https://marketplace.visualstudio.com/items?itemName=your-publisher.tomcat-manager) 安装

2. **创建Tomcat实例**
   - 打开命令面板 (`Ctrl+Shift+P`)
   - 运行 "Tomcat Manager: Create Instance"
   - 按照设置向导操作

3. **部署您的项目**
   - 右键点击您的Maven/Gradle项目
   - 选择 "Deploy to Tomcat"
   - 选择目标Tomcat实例

4. **开始开发**
   - **普通模式**: 点击 ▶️ **启动** 按钮运行应用程序
   - **调试模式**: 点击 🐛 **调试** 按钮进行断点调试
   - **热部署**: 文件更改时自动部署

5. **调试您的应用程序**
   - 在Java代码中设置断点 (点击行号左侧)
   - 以调试模式启动Tomcat (🐛 调试按钮)
   - 按 `F5` 连接调试器 (launch.json自动创建)
   - 访问Web应用程序触发断点

## 📋 系统要求

- **VSCode** 1.74.0 或更高版本
- **Apache Tomcat** 8.5+ 或 9.0+
- **Java** 8 或更高版本
- **Maven** 或 **Gradle** (用于项目构建)

## 🎯 支持的项目类型

### 📦 单模块项目
- ✅ Maven Web项目 (WAR打包)
- ✅ Gradle Web项目 (WAR打包)
- ✅ 标准Java Web应用程序
- ✅ Spring Boot Web应用程序
- ✅ JSP/Servlet应用程序

### 🏗️ 多模块项目
- ✅ Maven多模块项目 (包含web模块)
- ✅ 父子POM结构
- ✅ 跨模块依赖 (自动解析)
- ✅ 大型项目 (10+模块，优化构建)

### 🐛 调试支持
- ✅ Java 8, 11, 17, 21+ (自动JDWP配置)
- ✅ 源码调试 (正确的编译设置)
- ✅ 远程调试功能
- ✅ 条件断点和日志断点

## 🔧 配置选项

扩展提供多种配置Tomcat实例的方式：

### 实例配置
- **端口**: HTTP, HTTPS, AJP, JMX, 管理端口, 调试端口 (自动分配)
- **JVM设置**: 内存, 参数, 调试选项, Java版本检测
- **浏览器**: 自动启动, 自定义浏览器, 默认页面
- **热部署**: 文件监控, 构建自动化, 增量更新
- **调试设置**: 自动JDWP配置, launch.json生成

### 全局设置
- **默认路径**: Tomcat安装路径, JRE位置
- **端口范围**: 自动端口分配 (包括调试端口)
- **构建设置**: Maven/Gradle配置, 多模块支持
- **界面偏好**: 语言, 通知, 刷新间隔
- **调试偏好**: 自动连接选项, 断点设置

## 📚 文档

全面的文档可在 [`docs/`](docs/) 目录中找到：

### 📖 用户指南
- **[使用指南](docs/USAGE.md)** - 详细使用说明
- **[设置面板指南](docs/SETTINGS_PANEL_GUIDE.md)** - 全局设置配置
- **[实例配置指南](docs/INSTANCE_CONFIG_GUIDE.md)** - 实例特定设置

### 🎓 教程 (中英文)
- **[快速开始](docs/tutorials/getting-started.md)** | **[Getting Started](docs/tutorials/getting-started-zh.md)**
- **[调试教程](docs/tutorials/debug-tutorial-zh.md)** | **[Debug Tutorial](docs/tutorials/debug-tutorial.md)**
- **[多模块项目](docs/tutorials/multi-module-tutorial-zh.md)** | **[Multi-Module Projects](docs/tutorials/multi-module-tutorial.md)**
- **[热部署指南](docs/tutorials/hot-deployment-tutorial-zh.md)** | **[Hot Deployment](docs/tutorials/hot-deployment-tutorial.md)**

### 🔧 技术文档
- **[热部署实现](docs/HOT_DEPLOY_IMPLEMENTATION.md)** - 热部署系统
- **[调试支持](docs/TOMCAT_DEBUG_SUPPORT.md)** - 调试功能实现
- **[多模块支持](docs/MAVEN_MULTI_MODULE_SUPPORT.md)** - Maven多模块项目支持
- **[构建优化](docs/LARGE_PROJECT_BUILD_OPTIMIZATION.md)** - 大型项目构建策略
- **[模板系统](docs/TEMPLATE_REFACTORING_GUIDE.md)** - HTML模板架构
- **[项目结构](docs/PROJECT_STRUCTURE.md)** - 代码库组织

### 🐛 故障排除
- **[启动问题](docs/TOMCAT_STARTUP_DIAGNOSTICS.md)** - Tomcat启动问题
- **[调试问题](docs/DEBUG_JDWP_TRANSPORT_FIX.md)** - 调试连接问题
- **[端口冲突](docs/PORT_DETECTION_GUIDE.md)** - 端口管理问题
- **[构建失败](docs/BUILD_FAILURE_DIAGNOSTICS.md)** - 项目构建问题
- **[热部署问题](docs/HOT_DEPLOY_ERROR_HANDLING_FIX.md)** - 热部署故障排除

## 🤝 贡献

我们欢迎贡献！请查看我们的 [贡献指南](docs/CONTRIBUTING.md) 了解详情。

### 开发环境设置

1. **克隆仓库**
   ```bash
   git clone https://github.com/your-username/tomcat-manager-vscode.git
   cd tomcat-manager-vscode
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **构建扩展**
   ```bash
   npm run compile
   ```

4. **开发模式运行**
   - 在VSCode中按 `F5` 启动扩展开发主机

## 📝 更新日志

查看 [CHANGELOG.md](docs/CHANGELOG.md) 了解详细的更改历史。

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- Apache Tomcat团队提供的优秀servlet容器
- VSCode团队提供的可扩展编辑器平台
- 提供反馈和改进的贡献者和用户

## 📞 支持

- 🐛 **错误报告**: [GitHub Issues](https://github.com/your-username/tomcat-manager-vscode/issues)
- 💡 **功能请求**: [GitHub Discussions](https://github.com/your-username/tomcat-manager-vscode/discussions)
- 📧 **邮件**: [<EMAIL>](mailto:<EMAIL>)

---

**为Java开发社区用❤️制作**
