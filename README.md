# 🚀 Tomcat Manager for VSCode

A comprehensive VSCode extension for managing Apache Tomcat servers with advanced features like hot deployment, project management, and graphical configuration.

**[中文文档 (Chinese Documentation)](README-zh.md)**

## ✨ Features

### 🚀 Core Features

- 🔧 **Multiple Tomcat Instances** - Create and manage multiple Tomcat server instances
- 🚀 **Hot Deployment** - Automatic deployment when files change with intelligent file type detection
- 📦 **Project Management** - Scan and deploy Maven/Gradle web projects with multi-module support
- 🎨 **Graphical Interface** - User-friendly web-based configuration panels
- 🔍 **Port Management** - Automatic port detection and conflict resolution
- 📊 **Real-time Monitoring** - Live status updates and log viewing
- 🌐 **Browser Integration** - Automatic browser launching with custom settings

### 🐛 Debug Features

- 🐛 **One-Click Debug** - Separate Debug button for easy debugging mode
- 🔧 **Auto Debug Configuration** - Automatic creation of VSCode launch.json
- 🎯 **Java Version Detection** - Smart JDWP parameter generation for different Java versions
- 🔄 **Legacy Instance Support** - Automatic upgrade of existing instances for debug support

### 🏗️ Build Optimization

- ⚡ **Large Project Optimization** - Smart build strategies for multi-module projects (10+ modules)
- 📦 **Incremental Building** - Skip unnecessary rebuilds when WAR is up-to-date
- 🔄 **Parallel Compilation** - Multi-threaded building for faster deployment
- 🎯 **Maven Multi-Module Support** - Intelligent dependency resolution without manual install

### 📋 Enhanced Logging

- 🎨 **Smart Log Classification** - Distinguish between errors and normal operations
- 📊 **Detailed Startup Information** - Comprehensive instance status reporting
- 🔍 **Debug Information Display** - Clear debug port and connection details

## 🚀 Quick Start

1. **Install the Extension**

   - Search for "Tomcat Manager" in VSCode Extensions
   - Or install from [VSCode Marketplace](https://marketplace.visualstudio.com/items?itemName=your-publisher.tomcat-manager)

2. **Create a Tomcat Instance**

   - Open Command Palette (`Ctrl+Shift+P`)
   - Run "Tomcat Manager: Create Instance"
   - Follow the guided setup wizard

3. **Deploy Your Project**

   - Right-click on your Maven/Gradle project
   - Select "Deploy to Tomcat"
   - Choose your target Tomcat instance

4. **Start Development**

   - **Normal Mode**: Click ▶️ **Start** button to run your application
   - **Debug Mode**: Click 🐛 **Debug** button for debugging with breakpoints
   - **Hot Deploy**: File changes are automatically deployed while running

5. **Debug Your Application**
   - Set breakpoints in your Java code (click line number left side)
   - Start Tomcat in debug mode (🐛 Debug button)
   - Press `F5` to connect debugger (launch.json auto-created)
   - Access your web application to trigger breakpoints

## 📋 Requirements

- **VSCode** 1.74.0 or higher
- **Apache Tomcat** 8.5+ or 9.0+
- **Java** 8 or higher
- **Maven** or **Gradle** (for project building)

## 🎯 Supported Project Types

### 📦 Single Module Projects

- ✅ Maven Web Projects (WAR packaging)
- ✅ Gradle Web Projects (WAR packaging)
- ✅ Standard Java Web Applications
- ✅ Spring Boot Web Applications
- ✅ JSP/Servlet Applications

### 🏗️ Multi-Module Projects

- ✅ Maven Multi-Module Projects (with web modules)
- ✅ Parent-Child POM structures
- ✅ Cross-module dependencies (automatic resolution)
- ✅ Large projects (10+ modules with optimized building)

### 🐛 Debug Support

- ✅ Java 8, 11, 17, 21+ (automatic JDWP configuration)
- ✅ Source code debugging (with proper compilation settings)
- ✅ Remote debugging capabilities
- ✅ Conditional and log breakpoints

## 🔧 Configuration

The extension provides multiple ways to configure your Tomcat instances:

### Instance Configuration

- **Ports**: HTTP, HTTPS, AJP, JMX, Management, Debug (auto-assigned)
- **JVM Settings**: Memory, arguments, debug options, Java version detection
- **Browser**: Auto-launch, custom browser, default pages
- **Hot Deploy**: File watching, build automation, incremental updates
- **Debug Settings**: Automatic JDWP configuration, launch.json generation

### Global Settings

- **Default Paths**: Tomcat installation, JRE location
- **Port Ranges**: Automatic port allocation (including debug ports)
- **Build Settings**: Maven/Gradle configuration, multi-module support
- **UI Preferences**: Language, notifications, refresh intervals
- **Debug Preferences**: Auto-connect options, breakpoint settings

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](docs/) directory:

### 📖 User Guides

- **[Usage Guide](docs/USAGE.md)** - Detailed usage instructions
- **[Settings Panel Guide](docs/SETTINGS_PANEL_GUIDE.md)** - Global settings configuration
- **[Instance Configuration Guide](docs/INSTANCE_CONFIG_GUIDE.md)** - Instance-specific settings

### 🎓 Tutorials (中英文教程)

- **[Getting Started](docs/tutorials/getting-started.md)** | **[快速开始](docs/tutorials/getting-started-zh.md)**
- **[Debug Tutorial](docs/tutorials/debug-tutorial.md)** | **[调试教程](docs/tutorials/debug-tutorial-zh.md)**
- **[Multi-Module Projects](docs/tutorials/multi-module-tutorial.md)** | **[多模块项目](docs/tutorials/multi-module-tutorial-zh.md)**
- **[Hot Deployment](docs/tutorials/hot-deployment-tutorial.md)** | **[热部署指南](docs/tutorials/hot-deployment-tutorial-zh.md)**

### 🔧 Technical Documentation

- **[Hot Deploy Implementation](docs/HOT_DEPLOY_IMPLEMENTATION.md)** - Hot deployment system
- **[Debug Support](docs/TOMCAT_DEBUG_SUPPORT.md)** - Debug functionality implementation
- **[Multi-Module Support](docs/MAVEN_MULTI_MODULE_SUPPORT.md)** - Maven multi-module project support
- **[Build Optimization](docs/LARGE_PROJECT_BUILD_OPTIMIZATION.md)** - Large project build strategies
- **[Template System](docs/TEMPLATE_REFACTORING_GUIDE.md)** - HTML template architecture
- **[Project Structure](docs/PROJECT_STRUCTURE.md)** - Codebase organization

### 🐛 Troubleshooting

- **[Startup Issues](docs/TOMCAT_STARTUP_DIAGNOSTICS.md)** - Tomcat startup problems
- **[Debug Issues](docs/DEBUG_JDWP_TRANSPORT_FIX.md)** - Debug connection problems
- **[Port Conflicts](docs/PORT_DETECTION_GUIDE.md)** - Port management issues
- **[Build Failures](docs/BUILD_FAILURE_DIAGNOSTICS.md)** - Project build problems
- **[Hot Deploy Issues](docs/HOT_DEPLOY_ERROR_HANDLING_FIX.md)** - Hot deployment troubleshooting

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/CONTRIBUTING.md) for details.

### Development Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/your-username/tomcat-manager-vscode.git
   cd tomcat-manager-vscode
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Build the extension**

   ```bash
   npm run compile
   ```

4. **Run in development mode**
   - Press `F5` in VSCode to launch Extension Development Host

## 📝 Changelog

See [CHANGELOG.md](docs/CHANGELOG.md) for a detailed history of changes.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Apache Tomcat team for the excellent servlet container
- VSCode team for the extensible editor platform
- Contributors and users who provide feedback and improvements

## 📞 Support

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/your-username/tomcat-manager-vscode/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/your-username/tomcat-manager-vscode/discussions)
- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)

---

## Made with ❤️ for the Java development community
