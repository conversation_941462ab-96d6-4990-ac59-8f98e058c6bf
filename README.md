# 🚀 Tomcat Manager for VSCode

A comprehensive VSCode extension for managing Apache Tomcat servers with advanced features like hot deployment, project management, and graphical configuration.

## ✨ Features

- 🔧 **Multiple Tomcat Instances** - Create and manage multiple Tomcat server instances
- 🚀 **Hot Deployment** - Automatic deployment when files change
- 📦 **Project Management** - Scan and deploy Maven/Gradle web projects
- 🎨 **Graphical Interface** - User-friendly web-based configuration panels
- 🔍 **Port Management** - Automatic port detection and conflict resolution
- 📊 **Real-time Monitoring** - Live status updates and log viewing
- 🌐 **Browser Integration** - Automatic browser launching with custom settings

## 🚀 Quick Start

1. **Install the Extension**
   - Search for "Tomcat Manager" in VSCode Extensions
   - Or install from [VSCode Marketplace](https://marketplace.visualstudio.com/items?itemName=your-publisher.tomcat-manager)

2. **Create a Tomcat Instance**
   - Open Command Palette (`Ctrl+Shift+P`)
   - Run "Tomcat Manager: Create Instance"
   - Follow the guided setup wizard

3. **Deploy Your Project**
   - Right-click on your Maven/Gradle project
   - Select "Deploy to Tom<PERSON>"
   - Choose your target Tomcat instance

## 📋 Requirements

- **VSCode** 1.74.0 or higher
- **Apache Tomcat** 8.5+ or 9.0+
- **Java** 8 or higher
- **Maven** or **Gradle** (for project building)

## 🎯 Supported Project Types

- ✅ Maven Web Projects (WAR packaging)
- ✅ Gradle Web Projects (WAR packaging)
- ✅ Standard Java Web Applications
- ✅ Spring Boot Web Applications
- ✅ JSP/Servlet Applications

## 🔧 Configuration

The extension provides multiple ways to configure your Tomcat instances:

### Instance Configuration
- **Ports**: HTTP, HTTPS, AJP, JMX, Management
- **JVM Settings**: Memory, arguments, debug options
- **Browser**: Auto-launch, custom browser, default pages
- **Hot Deploy**: File watching, build automation

### Global Settings
- **Default Paths**: Tomcat installation, JRE location
- **Port Ranges**: Automatic port allocation
- **Build Settings**: Maven/Gradle configuration
- **UI Preferences**: Language, notifications, refresh intervals

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](docs/) directory:

### 📖 User Guides
- **[Usage Guide](docs/USAGE.md)** - Detailed usage instructions
- **[Settings Panel Guide](docs/SETTINGS_PANEL_GUIDE.md)** - Global settings configuration
- **[Instance Configuration Guide](docs/INSTANCE_CONFIG_GUIDE.md)** - Instance-specific settings

### 🔧 Technical Documentation
- **[Hot Deploy Implementation](docs/HOT_DEPLOY_IMPLEMENTATION.md)** - Hot deployment system
- **[Template System](docs/TEMPLATE_REFACTORING_GUIDE.md)** - HTML template architecture
- **[Project Structure](docs/PROJECT_STRUCTURE.md)** - Codebase organization

### 🐛 Troubleshooting
- **[Startup Issues](docs/TOMCAT_STARTUP_DIAGNOSTICS.md)** - Tomcat startup problems
- **[Port Conflicts](docs/PORT_DETECTION_GUIDE.md)** - Port management issues
- **[Build Failures](docs/BUILD_FAILURE_DIAGNOSTICS.md)** - Project build problems

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/CONTRIBUTING.md) for details.

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/tomcat-manager-vscode.git
   cd tomcat-manager-vscode
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the extension**
   ```bash
   npm run compile
   ```

4. **Run in development mode**
   - Press `F5` in VSCode to launch Extension Development Host

## 📝 Changelog

See [CHANGELOG.md](docs/CHANGELOG.md) for a detailed history of changes.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Apache Tomcat team for the excellent servlet container
- VSCode team for the extensible editor platform
- Contributors and users who provide feedback and improvements

## 📞 Support

- 🐛 **Bug Reports**: [GitHub Issues](https://github.com/your-username/tomcat-manager-vscode/issues)
- 💡 **Feature Requests**: [GitHub Discussions](https://github.com/your-username/tomcat-manager-vscode/discussions)
- 📧 **Email**: <EMAIL>

---

**Made with ❤️ for the Java development community**
