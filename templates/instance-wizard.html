<div class="header">
    <h1>🚀 创建Tomcat实例</h1>
    <p>创建一个新的Tomcat服务器实例，配置端口、JVM参数等设置</p>
</div>

<div id="alert-container"></div>

<form id="instance-form">
    <div class="card">
        <div class="card-header">基本信息</div>
        
        <div class="form-group">
            <label for="name">实例名称 *</label>
            <input type="text" id="name" name="name" placeholder="例如: MyApp-Dev" required>
            <div class="help-text">为实例指定一个有意义的名称</div>
        </div>
        
        <div class="form-group">
            <label for="description">描述</label>
            <textarea id="description" name="description" placeholder="实例的详细描述（可选）"></textarea>
        </div>
        
        <div class="form-group">
            <label for="baseTomcatPath">基础Tomcat路径 *</label>
            <div style="display: flex; gap: 8px;">
                <input type="text" id="baseTomcatPath" name="baseTomcatPath" placeholder="/usr/local/tomcat" required style="flex: 1;">
                <button type="button" class="button button-secondary" onclick="selectTomcatPath()" style="white-space: nowrap;">
                    📁 选择文件夹
                </button>
            </div>
            <div class="help-text">指向Tomcat安装目录的路径</div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">端口配置</div>
        
        <div class="button-group" style="margin-top: 0; padding-top: 0; border-top: none;">
            <button type="button" class="button button-secondary" onclick="suggestPorts()">
                🎯 自动分配端口
            </button>
        </div>
        
        <div class="port-grid">
            <div class="form-group">
                <label for="httpPort">HTTP端口</label>
                <input type="number" id="httpPort" name="httpPort" value="8080" min="1024" max="65535">
            </div>
            
            <div class="form-group">
                <label for="httpsPort">HTTPS端口</label>
                <input type="number" id="httpsPort" name="httpsPort" value="8443" min="1024" max="65535">
            </div>
            
            <div class="form-group">
                <label for="ajpPort">AJP端口</label>
                <input type="number" id="ajpPort" name="ajpPort" value="8009" min="1024" max="65535">
            </div>
            
            <div class="form-group">
                <label for="jmxPort">JMX端口</label>
                <input type="number" id="jmxPort" name="jmxPort" value="9999" min="1024" max="65535">
            </div>
            
            <div class="form-group">
                <label for="shutdownPort">管理端口</label>
                <input type="number" id="shutdownPort" name="shutdownPort" value="8005" min="1024" max="65535">
            </div>
        </div>
        
        <div id="port-validation"></div>
    </div>
    
    <div class="card">
        <div class="card-header">JVM配置</div>
        
        <div class="form-group">
            <label for="jrePath">JRE路径</label>
            <div style="display: flex; gap: 8px;">
                <input type="text" id="jrePath" name="jrePath" placeholder="留空使用默认JRE" style="flex: 1;">
                <button type="button" class="button button-secondary" onclick="loadDefaultJrePath()" style="white-space: nowrap;">
                    🔄 读取默认
                </button>
                <button type="button" class="button button-secondary" onclick="selectJrePath()" style="white-space: nowrap;">
                    📁 选择文件夹
                </button>
            </div>
            <div class="help-text">指定此实例使用的Java运行环境，留空使用系统默认</div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="minHeapSize">最小堆内存</label>
                <input type="text" id="minHeapSize" name="minHeapSize" value="256m" placeholder="256m">
            </div>
            
            <div class="form-group">
                <label for="maxHeapSize">最大堆内存</label>
                <input type="text" id="maxHeapSize" name="maxHeapSize" value="512m" placeholder="512m">
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">浏览器设置</div>
        
        <div class="checkbox-group">
            <input type="checkbox" id="autoOpenBrowser" name="autoOpenBrowser" checked>
            <label for="autoOpenBrowser">启动时自动打开浏览器</label>
        </div>
        
        <div class="form-group">
            <label for="browserType">浏览器类型</label>
            <select id="browserType" name="browserType">
                <option value="default">系统默认</option>
                <option value="chrome">Google Chrome</option>
                <option value="firefox">Mozilla Firefox</option>
                <option value="safari">Safari</option>
                <option value="edge">Microsoft Edge</option>
                <option value="custom">自定义</option>
            </select>
        </div>
        
        <div class="form-group" id="customBrowserPath" style="display: none;">
            <label for="customPath">自定义浏览器路径</label>
            <input type="text" id="customPath" name="customPath" placeholder="浏览器可执行文件路径">
        </div>
        
        <div class="form-group">
            <label for="defaultPage">默认启动页面</label>
            <input type="text" id="defaultPage" name="defaultPage" placeholder="例如: /index.html">
            <div class="help-text">启动后在浏览器中打开的页面路径</div>
        </div>
    </div>
    
    <div class="loading">
        <div class="spinner"></div>
        正在创建实例...
    </div>
    
    <div class="button-group">
        <button type="submit" class="button button-primary">创建实例</button>
        <button type="button" class="button button-secondary" onclick="window.close()">取消</button>
    </div>
</form>
