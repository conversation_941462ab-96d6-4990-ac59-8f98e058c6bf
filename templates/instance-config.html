<div class="header">
  <h1>⚙️ 配置Tomcat实例</h1>
  <p>修改实例 <strong>{{instanceName}}</strong> 的配置参数</p>
</div>

<div id="alert-container"></div>

<div class="tabs">
  <div class="tab active" data-tab="basic-info">基本信息</div>
  <div class="tab" data-tab="port-config">端口配置</div>
  <div class="tab" data-tab="jvm-config">JVM配置</div>
  <div class="tab" data-tab="browser-config">浏览器配置</div>
  <div class="tab" data-tab="advanced-config">高级配置</div>
</div>

<form id="config-form">
  <div id="basic-info" class="tab-content active">
    <div class="card">
      <div class="card-header">基本信息</div>

      <div class="form-group">
        <label for="instanceName">实例名称 *</label>
        <input type="text" id="instanceName" name="instanceName" required />
        <div class="help-text">实例的显示名称</div>
      </div>

      <div class="form-group">
        <label for="description">描述</label>
        <textarea
          id="description"
          name="description"
          placeholder="实例的详细描述"
        ></textarea>
      </div>

      <div class="form-group">
        <label for="baseTomcatPath">基础Tomcat路径 *</label>
        <div style="display: flex; gap: 8px">
          <input
            type="text"
            id="baseTomcatPath"
            name="baseTomcatPath"
            required
            style="flex: 1"
            readonly
          />
          <button
            type="button"
            class="button button-secondary"
            onclick="selectTomcatPath()"
          >
            📁 更改路径
          </button>
        </div>
        <div class="help-text">Tomcat安装目录的路径</div>
      </div>

      <div class="form-group">
        <label for="status">当前状态</label>
        <div style="display: flex; align-items: center; gap: 10px">
          <span id="status-indicator" class="status-indicator"></span>
          <span id="status-text">未知</span>
          <button
            type="button"
            class="button button-secondary"
            onclick="refreshStatus()"
          >
            🔄 刷新状态
          </button>
        </div>
      </div>
    </div>
  </div>

  <div id="port-config" class="tab-content">
    <div class="card">
      <div class="card-header">端口配置</div>

      <div
        class="button-group"
        style="margin-top: 0; padding-top: 0; border-top: none"
      >
        <button
          type="button"
          class="button button-secondary"
          onclick="suggestPorts()"
        >
          🎯 重新分配端口
        </button>
        <button
          type="button"
          class="button button-secondary"
          onclick="checkPortsAvailability()"
        >
          🔍 检查端口可用性
        </button>
      </div>

      <div class="port-grid">
        <div class="form-group">
          <label for="httpPort">HTTP端口</label>
          <input
            type="number"
            id="httpPort"
            name="httpPort"
            min="1024"
            max="65535"
          />
          <div class="help-text">Web访问端口</div>
        </div>

        <div class="form-group">
          <label for="httpsPort">HTTPS端口</label>
          <input
            type="number"
            id="httpsPort"
            name="httpsPort"
            min="1024"
            max="65535"
          />
          <div class="help-text">安全Web访问端口</div>
        </div>

        <div class="form-group">
          <label for="ajpPort">AJP端口</label>
          <input
            type="number"
            id="ajpPort"
            name="ajpPort"
            min="1024"
            max="65535"
          />
          <div class="help-text">Apache连接器端口</div>
        </div>

        <div class="form-group">
          <label for="jmxPort">JMX端口</label>
          <input
            type="number"
            id="jmxPort"
            name="jmxPort"
            min="1024"
            max="65535"
          />
          <div class="help-text">JMX监控端口</div>
        </div>

        <div class="form-group">
          <label for="shutdownPort">管理端口</label>
          <input
            type="number"
            id="shutdownPort"
            name="shutdownPort"
            min="1024"
            max="65535"
          />
          <div class="help-text">Tomcat管理端口</div>
        </div>
      </div>

      <div id="port-validation"></div>
    </div>
  </div>

  <div id="jvm-config" class="tab-content">
    <div class="card">
      <div class="card-header">JVM配置</div>

      <div class="form-group">
        <label for="jrePath">JRE路径</label>
        <div style="display: flex; gap: 8px">
          <input
            type="text"
            id="jrePath"
            name="jrePath"
            placeholder="留空使用默认JRE"
            style="flex: 1"
          />
          <button
            type="button"
            class="button button-secondary"
            onclick="loadDefaultJrePath()"
          >
            🔄 读取默认
          </button>
          <button
            type="button"
            class="button button-secondary"
            onclick="selectJrePath()"
          >
            📁 选择文件夹
          </button>
        </div>
        <div class="help-text">指定此实例使用的Java运行环境</div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="minHeapSize">最小堆内存</label>
          <input
            type="text"
            id="minHeapSize"
            name="minHeapSize"
            placeholder="256m"
          />
          <div class="help-text">-Xms参数</div>
        </div>

        <div class="form-group">
          <label for="maxHeapSize">最大堆内存</label>
          <input
            type="text"
            id="maxHeapSize"
            name="maxHeapSize"
            placeholder="512m"
          />
          <div class="help-text">-Xmx参数</div>
        </div>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="permGenSize">永久代大小</label>
          <input
            type="text"
            id="permGenSize"
            name="permGenSize"
            placeholder="128m"
          />
          <div class="help-text">-XX:PermSize参数（Java 7及以下）</div>
        </div>

        <div class="form-group">
          <label for="metaspaceSize">元空间大小</label>
          <input
            type="text"
            id="metaspaceSize"
            name="metaspaceSize"
            placeholder="128m"
          />
          <div class="help-text">-XX:MetaspaceSize参数（Java 8+）</div>
        </div>
      </div>

      <div class="form-group">
        <label for="additionalJvmArgs">其他JVM参数</label>
        <textarea
          id="additionalJvmArgs"
          name="additionalJvmArgs"
          placeholder="例如: -Dfile.encoding=UTF-8 -Djava.awt.headless=true"
        ></textarea>
        <div class="help-text">每行一个参数，或用空格分隔</div>
      </div>
    </div>
  </div>

  <div id="browser-config" class="tab-content">
    <div class="card">
      <div class="card-header">浏览器配置</div>

      <div class="checkbox-group">
        <input type="checkbox" id="autoOpenBrowser" name="autoOpenBrowser" />
        <label for="autoOpenBrowser">启动时自动打开浏览器</label>
      </div>

      <div class="form-group">
        <label for="browserType">浏览器类型</label>
        <select id="browserType" name="browserType">
          <option value="default">系统默认</option>
          <option value="chrome">Google Chrome</option>
          <option value="firefox">Mozilla Firefox</option>
          <option value="safari">Safari</option>
          <option value="edge">Microsoft Edge</option>
          <option value="custom">自定义</option>
        </select>
      </div>

      <div class="form-group" id="customBrowserPath" style="display: none">
        <label for="customPath">自定义浏览器路径</label>
        <div style="display: flex; gap: 8px">
          <input
            type="text"
            id="customPath"
            name="customPath"
            placeholder="浏览器可执行文件路径"
            style="flex: 1"
          />
          <button
            type="button"
            class="button button-secondary"
            onclick="selectBrowserPath()"
          >
            📁 选择
          </button>
        </div>
      </div>

      <div class="form-group">
        <label for="defaultPage">默认启动页面</label>
        <input
          type="text"
          id="defaultPage"
          name="defaultPage"
          placeholder="例如: /index.html"
        />
        <div class="help-text">启动后在浏览器中打开的页面路径</div>
      </div>
    </div>
  </div>

  <div id="advanced-config" class="tab-content">
    <div class="card">
      <div class="card-header">高级配置</div>

      <div class="form-group">
        <label for="startupTimeout">启动超时时间 (秒)</label>
        <input
          type="number"
          id="startupTimeout"
          name="startupTimeout"
          value="60"
          min="10"
          max="300"
        />
        <div class="help-text">等待Tomcat启动的最大时间</div>
      </div>

      <div class="form-group">
        <label for="shutdownTimeout">关闭超时时间 (秒)</label>
        <input
          type="number"
          id="shutdownTimeout"
          name="shutdownTimeout"
          value="30"
          min="5"
          max="120"
        />
        <div class="help-text">等待Tomcat关闭的最大时间</div>
      </div>

      <div class="checkbox-group">
        <input type="checkbox" id="enableJmx" name="enableJmx" />
        <label for="enableJmx">启用JMX监控</label>
      </div>

      <div class="checkbox-group">
        <input type="checkbox" id="enableDebug" name="enableDebug" />
        <label for="enableDebug">启用远程调试</label>
      </div>

      <div class="form-group" id="debugPortGroup" style="display: none">
        <label for="debugPort">调试端口</label>
        <input
          type="number"
          id="debugPort"
          name="debugPort"
          value="5005"
          min="1024"
          max="65535"
        />
        <div class="help-text">远程调试监听端口</div>
      </div>

      <div class="form-group">
        <label for="workingDirectory">工作目录</label>
        <input
          type="text"
          id="workingDirectory"
          name="workingDirectory"
          readonly
        />
        <div class="help-text">Tomcat实例的工作目录</div>
      </div>
    </div>
  </div>

  <div class="loading">
    <div class="spinner"></div>
    <span id="loading-text">正在处理...</span>
  </div>

  <div class="button-group">
    <button type="submit" class="button button-primary">保存配置</button>
    <button
      type="button"
      class="button button-secondary"
      onclick="resetToOriginal()"
    >
      重置
    </button>
    <button
      type="button"
      class="button button-secondary"
      onclick="window.close()"
    >
      取消
    </button>
  </div>
</form>
