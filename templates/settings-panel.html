<div class="header">
    <h1>⚙️ Tomcat Manager 设置</h1>
    <p>配置插件的全局设置和默认参数</p>
</div>

<div id="alert-container"></div>

<div class="tabs">
    <div class="tab active" data-tab="general-settings">常规设置</div>
    <div class="tab" data-tab="path-settings">路径配置</div>
    <div class="tab" data-tab="port-settings">端口配置</div>
    <div class="tab" data-tab="deploy-settings">部署设置</div>
</div>

<form id="settings-form">
    <div id="general-settings" class="tab-content active">
        <div class="card">
            <div class="card-header">常规设置</div>
            
            <div class="form-group">
                <label for="language">界面语言</label>
                <select id="language" name="language">
                    <option value="zh-CN">中文（简体）</option>
                    <option value="en-US">English</option>
                </select>
                <div class="help-text">选择插件界面显示语言</div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="autoRefresh" name="autoRefresh" checked>
                <label for="autoRefresh">自动刷新实例状态</label>
            </div>
            
            <div class="form-group">
                <label for="refreshInterval">刷新间隔 (秒)</label>
                <input type="number" id="refreshInterval" name="refreshInterval" value="30" min="5" max="300">
                <div class="help-text">自动刷新实例状态的时间间隔</div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="showNotifications" name="showNotifications" checked>
                <label for="showNotifications">显示通知消息</label>
            </div>
        </div>
    </div>

    <div id="path-settings" class="tab-content">
        <div class="card">
            <div class="card-header">默认路径配置</div>
            
            <div class="form-group">
                <label for="defaultTomcatPath">默认Tomcat路径</label>
                <div style="display: flex; gap: 8px;">
                    <input type="text" id="defaultTomcatPath" name="defaultTomcatPath" placeholder="/usr/local/tomcat" style="flex: 1;">
                    <button type="button" class="button button-secondary" onclick="selectDefaultTomcatPath()">
                        📁 选择
                    </button>
                </div>
                <div class="help-text">新建实例时的默认Tomcat安装路径</div>
            </div>
            
            <div class="form-group">
                <label for="defaultJrePath">默认JRE路径</label>
                <div style="display: flex; gap: 8px;">
                    <input type="text" id="defaultJrePath" name="defaultJrePath" placeholder="留空使用系统默认" style="flex: 1;">
                    <button type="button" class="button button-secondary" onclick="detectDefaultJrePath()">
                        🔍 检测
                    </button>
                    <button type="button" class="button button-secondary" onclick="selectDefaultJrePath()">
                        📁 选择
                    </button>
                </div>
                <div class="help-text">新建实例时的默认JRE路径</div>
            </div>
            
            <div class="form-group">
                <label for="workspaceRoot">工作区根目录</label>
                <input type="text" id="workspaceRoot" name="workspaceRoot" readonly>
                <div class="help-text">当前VSCode工作区的根目录</div>
            </div>
        </div>
    </div>

    <div id="port-settings" class="tab-content">
        <div class="card">
            <div class="card-header">默认端口配置</div>
            
            <div class="port-grid">
                <div class="form-group">
                    <label for="defaultHttpPort">默认HTTP端口</label>
                    <input type="number" id="defaultHttpPort" name="defaultHttpPort" value="8080" min="1024" max="65535">
                </div>
                
                <div class="form-group">
                    <label for="defaultHttpsPort">默认HTTPS端口</label>
                    <input type="number" id="defaultHttpsPort" name="defaultHttpsPort" value="8443" min="1024" max="65535">
                </div>
                
                <div class="form-group">
                    <label for="defaultAjpPort">默认AJP端口</label>
                    <input type="number" id="defaultAjpPort" name="defaultAjpPort" value="8009" min="1024" max="65535">
                </div>
                
                <div class="form-group">
                    <label for="defaultJmxPort">默认JMX端口</label>
                    <input type="number" id="defaultJmxPort" name="defaultJmxPort" value="9999" min="1024" max="65535">
                </div>
                
                <div class="form-group">
                    <label for="defaultShutdownPort">默认管理端口</label>
                    <input type="number" id="defaultShutdownPort" name="defaultShutdownPort" value="8005" min="1024" max="65535">
                </div>
            </div>
            
            <div class="form-group">
                <label for="portRange">端口分配范围</label>
                <div class="form-row">
                    <div class="form-group">
                        <input type="number" id="portRangeStart" name="portRangeStart" value="8000" min="1024" max="65535" placeholder="起始端口">
                    </div>
                    <div class="form-group">
                        <input type="number" id="portRangeEnd" name="portRangeEnd" value="9000" min="1024" max="65535" placeholder="结束端口">
                    </div>
                </div>
                <div class="help-text">自动分配端口时的搜索范围</div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="autoPortIncrement" name="autoPortIncrement" checked>
                <label for="autoPortIncrement">自动递增端口号</label>
            </div>
        </div>
    </div>

    <div id="deploy-settings" class="tab-content">
        <div class="card">
            <div class="card-header">部署设置</div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="autoBuildEnabled" name="autoBuildEnabled" checked>
                <label for="autoBuildEnabled">默认启用自动构建</label>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="hotDeployEnabled" name="hotDeployEnabled" checked>
                <label for="hotDeployEnabled">默认启用热部署</label>
            </div>
            
            <div class="form-group">
                <label for="defaultWatchExtensions">默认监听文件扩展名</label>
                <input type="text" id="defaultWatchExtensions" name="defaultWatchExtensions" 
                       value="java,jsp,html,css,js,xml,properties">
                <div class="help-text">用逗号分隔，例如: java,jsp,html</div>
            </div>
            
            <div class="form-group">
                <label for="defaultExcludeDirectories">默认排除目录</label>
                <input type="text" id="defaultExcludeDirectories" name="defaultExcludeDirectories" 
                       value="target,build,node_modules,.git,.idea,.vscode">
                <div class="help-text">用逗号分隔，例如: target,build</div>
            </div>
            
            <div class="form-group">
                <label for="defaultDeployDelay">默认部署延迟 (毫秒)</label>
                <input type="number" id="defaultDeployDelay" name="defaultDeployDelay" value="1000" min="0" max="10000">
                <div class="help-text">文件变化后等待多长时间再部署</div>
            </div>
            
            <div class="form-group">
                <label for="maxBuildTime">最大构建时间 (秒)</label>
                <input type="number" id="maxBuildTime" name="maxBuildTime" value="300" min="30" max="1800">
                <div class="help-text">构建超时时间，超过此时间将终止构建</div>
            </div>
        </div>
    </div>

    <div class="loading">
        <div class="spinner"></div>
        <span id="loading-text">正在保存设置...</span>
    </div>

    <div class="button-group">
        <button type="submit" class="button button-primary">保存设置</button>
        <button type="button" class="button button-secondary" onclick="resetToDefaults()">恢复默认</button>
        <button type="button" class="button button-secondary" onclick="window.close()">取消</button>
    </div>
</form>
