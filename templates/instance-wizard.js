function initPage() {
    // 浏览器类型变化处理
    document.getElementById('browserType').addEventListener('change', function() {
        const customPath = document.getElementById('customBrowserPath');
        if (this.value === 'custom') {
            customPath.style.display = 'block';
        } else {
            customPath.style.display = 'none';
        }
    });
    
    // 端口变化时验证
    const portInputs = document.querySelectorAll('input[type="number"]');
    portInputs.forEach(input => {
        input.addEventListener('blur', validatePorts);
    });
    
    // 表单提交
    document.getElementById('instance-form').addEventListener('submit', function(e) {
        e.preventDefault();
        createInstance();
    });
}

function validatePorts() {
    const ports = {
        httpPort: parseInt(document.getElementById('httpPort').value),
        httpsPort: parseInt(document.getElementById('httpsPort').value),
        ajpPort: parseInt(document.getElementById('ajpPort').value),
        jmxPort: parseInt(document.getElementById('jmxPort').value),
        shutdownPort: parseInt(document.getElementById('shutdownPort').value)
    };
    
    sendMessage('validatePorts', ports);
}

function suggestPorts() {
    sendMessage('suggestPorts', {});
}

function selectTomcatPath() {
    sendMessage('selectTomcatPath', {});
}

function selectJrePath() {
    sendMessage('selectJrePath', {});
}

function loadDefaultJrePath() {
    sendMessage('loadDefaultJrePath', {});
}

function createInstance() {
    const formData = new FormData(document.getElementById('instance-form'));
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        baseTomcatPath: formData.get('baseTomcatPath'),
        ports: {
            httpPort: parseInt(formData.get('httpPort')),
            httpsPort: parseInt(formData.get('httpsPort')),
            ajpPort: parseInt(formData.get('ajpPort')),
            jmxPort: parseInt(formData.get('jmxPort')),
            shutdownPort: parseInt(formData.get('shutdownPort'))
        },
        jvm: {
            jrePath: formData.get('jrePath') || '',
            minHeapSize: formData.get('minHeapSize'),
            maxHeapSize: formData.get('maxHeapSize'),
            additionalArgs: []
        },
        browser: {
            type: formData.get('browserType'),
            autoOpen: formData.has('autoOpenBrowser'),
            defaultPage: formData.get('defaultPage'),
            customPath: formData.get('customPath')
        }
    };
    
    showLoading();
    sendMessage('createInstance', data);
}

function handleMessage(message) {
    switch (message.command) {
        case 'instanceCreated':
            hideLoading();
            if (message.data.success) {
                showAlert('实例创建成功！', 'success');
                setTimeout(() => window.close(), 2000);
            } else {
                showAlert('创建失败: ' + message.data.error, 'error');
            }
            break;
        case 'portsValidated':
            const validation = document.getElementById('port-validation');
            if (message.data.errors.length > 0) {
                validation.innerHTML = '<div class="alert alert-error">端口配置错误: ' + 
                    message.data.errors.join(', ') + '</div>';
            } else {
                validation.innerHTML = '<div class="alert alert-success">端口配置有效</div>';
            }
            break;
        case 'portsSuggested':
            if (message.data.ports) {
                const ports = message.data.ports;
                document.getElementById('httpPort').value = ports.httpPort;
                document.getElementById('httpsPort').value = ports.httpsPort;
                document.getElementById('ajpPort').value = ports.ajpPort;
                document.getElementById('jmxPort').value = ports.jmxPort;
                document.getElementById('shutdownPort').value = ports.shutdownPort;
                showAlert('已自动分配可用端口', 'success');
            }
            break;
        case 'tomcatPathSelected':
            if (message.data.path) {
                document.getElementById('baseTomcatPath').value = message.data.path;
                showAlert('Tomcat路径已选择', 'success');
            }
            break;
        case 'jrePathSelected':
            if (message.data.path) {
                document.getElementById('jrePath').value = message.data.path;
                showAlert('JRE路径已选择', 'success');
            }
            break;
        case 'defaultJrePathLoaded':
            if (message.data.path) {
                document.getElementById('jrePath').value = message.data.path;
                showAlert('已加载默认JRE路径', 'success');
            } else {
                showAlert('未找到默认JRE路径', 'warning');
            }
            break;
    }
}
