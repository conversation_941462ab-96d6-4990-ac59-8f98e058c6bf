function initPage() {
    // 表单提交处理
    document.getElementById('settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });
    
    // 加载当前设置
    loadCurrentSettings();
    
    // 设置工作区根目录（只读）
    if (typeof vscode !== 'undefined') {
        sendMessage('getWorkspaceRoot', {});
    }
}

function loadCurrentSettings() {
    showLoading();
    document.getElementById('loading-text').textContent = '正在加载设置...';
    sendMessage('loadSettings', {});
}

function saveSettings() {
    const formData = new FormData(document.getElementById('settings-form'));
    
    const settings = {
        general: {
            language: formData.get('language'),
            autoRefresh: formData.has('autoRefresh'),
            refreshInterval: parseInt(formData.get('refreshInterval')),
            showNotifications: formData.has('showNotifications')
        },
        paths: {
            defaultTomcatPath: formData.get('defaultTomcatPath'),
            defaultJrePath: formData.get('defaultJrePath')
        },
        ports: {
            defaultHttpPort: parseInt(formData.get('defaultHttpPort')),
            defaultHttpsPort: parseInt(formData.get('defaultHttpsPort')),
            defaultAjpPort: parseInt(formData.get('defaultAjpPort')),
            defaultJmxPort: parseInt(formData.get('defaultJmxPort')),
            defaultShutdownPort: parseInt(formData.get('defaultShutdownPort')),
            portRangeStart: parseInt(formData.get('portRangeStart')),
            portRangeEnd: parseInt(formData.get('portRangeEnd')),
            autoPortIncrement: formData.has('autoPortIncrement')
        },
        deploy: {
            autoBuildEnabled: formData.has('autoBuildEnabled'),
            hotDeployEnabled: formData.has('hotDeployEnabled'),
            defaultWatchExtensions: formData.get('defaultWatchExtensions').split(',').map(s => s.trim()),
            defaultExcludeDirectories: formData.get('defaultExcludeDirectories').split(',').map(s => s.trim()),
            defaultDeployDelay: parseInt(formData.get('defaultDeployDelay')),
            maxBuildTime: parseInt(formData.get('maxBuildTime'))
        }
    };
    
    showLoading();
    document.getElementById('loading-text').textContent = '正在保存设置...';
    sendMessage('updateSettings', settings);
}

function resetToDefaults() {
    if (confirm('确定要恢复所有设置到默认值吗？此操作不可撤销。')) {
        showLoading();
        document.getElementById('loading-text').textContent = '正在恢复默认设置...';
        sendMessage('resetToDefaults', {});
    }
}

function selectDefaultTomcatPath() {
    sendMessage('selectDefaultTomcatPath', {});
}

function selectDefaultJrePath() {
    sendMessage('selectDefaultJrePath', {});
}

function detectDefaultJrePath() {
    showLoading();
    document.getElementById('loading-text').textContent = '正在检测JRE路径...';
    sendMessage('detectDefaultJrePath', {});
}

function populateForm(settings) {
    // 常规设置
    if (settings.general) {
        document.getElementById('language').value = settings.general.language || 'zh-CN';
        document.getElementById('autoRefresh').checked = settings.general.autoRefresh !== false;
        document.getElementById('refreshInterval').value = settings.general.refreshInterval || 30;
        document.getElementById('showNotifications').checked = settings.general.showNotifications !== false;
    }
    
    // 路径设置
    if (settings.paths) {
        document.getElementById('defaultTomcatPath').value = settings.paths.defaultTomcatPath || '';
        document.getElementById('defaultJrePath').value = settings.paths.defaultJrePath || '';
    }
    
    // 端口设置
    if (settings.ports) {
        document.getElementById('defaultHttpPort').value = settings.ports.defaultHttpPort || 8080;
        document.getElementById('defaultHttpsPort').value = settings.ports.defaultHttpsPort || 8443;
        document.getElementById('defaultAjpPort').value = settings.ports.defaultAjpPort || 8009;
        document.getElementById('defaultJmxPort').value = settings.ports.defaultJmxPort || 9999;
        document.getElementById('defaultShutdownPort').value = settings.ports.defaultShutdownPort || 8005;
        document.getElementById('portRangeStart').value = settings.ports.portRangeStart || 8000;
        document.getElementById('portRangeEnd').value = settings.ports.portRangeEnd || 9000;
        document.getElementById('autoPortIncrement').checked = settings.ports.autoPortIncrement !== false;
    }
    
    // 部署设置
    if (settings.deploy) {
        document.getElementById('autoBuildEnabled').checked = settings.deploy.autoBuildEnabled !== false;
        document.getElementById('hotDeployEnabled').checked = settings.deploy.hotDeployEnabled !== false;
        document.getElementById('defaultWatchExtensions').value = 
            (settings.deploy.defaultWatchExtensions || ['java','jsp','html','css','js','xml','properties']).join(',');
        document.getElementById('defaultExcludeDirectories').value = 
            (settings.deploy.defaultExcludeDirectories || ['target','build','node_modules','.git','.idea','.vscode']).join(',');
        document.getElementById('defaultDeployDelay').value = settings.deploy.defaultDeployDelay || 1000;
        document.getElementById('maxBuildTime').value = settings.deploy.maxBuildTime || 300;
    }
}

function handleMessage(message) {
    switch (message.command) {
        case 'settingsLoaded':
            hideLoading();
            if (message.data.settings) {
                populateForm(message.data.settings);
            }
            break;
            
        case 'settingsUpdated':
            hideLoading();
            if (message.data.success) {
                showAlert('设置保存成功！', 'success');
            } else {
                showAlert('保存设置失败: ' + message.data.error, 'error');
            }
            break;
            
        case 'defaultsReset':
            hideLoading();
            if (message.data.success) {
                showAlert('已恢复默认设置', 'success');
                loadCurrentSettings(); // 重新加载设置
            } else {
                showAlert('恢复默认设置失败: ' + message.data.error, 'error');
            }
            break;
            
        case 'defaultTomcatPathSelected':
            if (message.data.path) {
                document.getElementById('defaultTomcatPath').value = message.data.path;
                showAlert('默认Tomcat路径已设置', 'success');
            }
            break;
            
        case 'defaultJrePathSelected':
            if (message.data.path) {
                document.getElementById('defaultJrePath').value = message.data.path;
                showAlert('默认JRE路径已设置', 'success');
            }
            break;
            
        case 'defaultJrePathDetected':
            hideLoading();
            if (message.data.path) {
                document.getElementById('defaultJrePath').value = message.data.path;
                showAlert('已检测到JRE路径', 'success');
            } else {
                showAlert('未能检测到JRE路径', 'warning');
            }
            break;
            
        case 'workspaceRootLoaded':
            if (message.data.path) {
                document.getElementById('workspaceRoot').value = message.data.path;
            }
            break;
    }
}
