let currentStep = 0;
let selectedProject = null;
let projects = [];

const steps = ['select-project', 'configure-deployment', 'deploy-progress'];

function initPage() {
    // 上下文路径变化处理
    document.getElementById('contextPath').addEventListener('change', function() {
        const customPath = document.getElementById('customContextPath');
        if (this.value === 'custom') {
            customPath.style.display = 'block';
        } else {
            customPath.style.display = 'none';
        }
    });
    
    // 热部署开关
    document.getElementById('enableHotDeploy').addEventListener('change', function() {
        const config = document.getElementById('hot-deploy-config');
        if (this.checked) {
            config.style.display = 'block';
        } else {
            config.style.display = 'none';
        }
    });
    
    updateStepButtons();
}

function scanProjects() {
    showLoading();
    document.getElementById('loading-text').textContent = '正在扫描项目...';
    sendMessage('scanProjects', {});
}

function selectProject(index) {
    selectedProject = projects[index];
    
    // 更新UI
    document.querySelectorAll('.project-item').forEach(item => {
        item.classList.remove('selected');
    });
    document.querySelector(`[data-index="${index}"]`).classList.add('selected');
    
    // 显示项目信息
    document.getElementById('selected-project-name').textContent = selectedProject.name;
    document.getElementById('selected-project-path').textContent = selectedProject.path;
    document.getElementById('selected-project-type').textContent = selectedProject.type;
    document.getElementById('selected-project-info').style.display = 'block';
    
    updateStepButtons();
}

function nextStep() {
    if (currentStep < steps.length - 1) {
        currentStep++;
        showStep(currentStep);
        updateStepButtons();
    }
}

function previousStep() {
    if (currentStep > 0) {
        currentStep--;
        showStep(currentStep);
        updateStepButtons();
    }
}

function showStep(step) {
    // 更新标签页
    document.querySelectorAll('.tab').forEach((tab, index) => {
        if (index === step) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });
    
    // 更新内容
    document.querySelectorAll('.tab-content').forEach((content, index) => {
        if (index === step) {
            content.classList.add('active');
        } else {
            content.classList.remove('active');
        }
    });
}

function updateStepButtons() {
    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');
    const deployBtn = document.getElementById('deploy-btn');
    
    // 上一步按钮
    if (currentStep > 0) {
        prevBtn.style.display = 'inline-block';
    } else {
        prevBtn.style.display = 'none';
    }
    
    // 下一步/部署按钮
    if (currentStep === steps.length - 1) {
        nextBtn.style.display = 'none';
        deployBtn.style.display = 'inline-block';
    } else {
        nextBtn.style.display = 'inline-block';
        deployBtn.style.display = 'none';
        
        // 检查是否可以进入下一步
        if (currentStep === 0) {
            nextBtn.disabled = !selectedProject;
        } else {
            nextBtn.disabled = false;
        }
    }
}

function startDeploy() {
    if (!selectedProject) {
        showAlert('请先选择一个项目', 'error');
        return;
    }
    
    const contextPath = document.getElementById('contextPath').value === 'custom' 
        ? document.getElementById('customPath').value 
        : document.getElementById('contextPath').value;
    
    const deployData = {
        instanceId: '{{instanceId}}',
        projectName: selectedProject.name,
        projectPath: selectedProject.path,
        projectType: selectedProject.type,
        contextPath: contextPath,
        enableHotDeploy: document.getElementById('enableHotDeploy').checked,
        hotDeployConfig: {
            watchExtensions: document.getElementById('watchExtensions').value.split(',').map(s => s.trim()),
            excludeDirectories: document.getElementById('excludeDirectories').value.split(',').map(s => s.trim()),
            deployDelay: parseInt(document.getElementById('deployDelay').value),
            restartApp: document.getElementById('restartApp').checked
        }
    };
    
    showLoading();
    document.getElementById('loading-text').textContent = '正在部署项目...';
    document.getElementById('deploy-log').style.display = 'block';
    
    sendMessage('deployProject', deployData);
}

function handleMessage(message) {
    switch (message.command) {
        case 'projectsScanned':
            hideLoading();
            projects = message.data.projects;
            renderProjectList(projects);
            break;
        case 'projectDeployed':
            hideLoading();
            if (message.data.success) {
                document.getElementById('deploy-status').innerHTML = 
                    '<div class="alert alert-success">项目部署成功！</div>';
                if (message.data.result.buildOutput) {
                    document.getElementById('log-content').value = message.data.result.buildOutput;
                }
            } else {
                document.getElementById('deploy-status').innerHTML = 
                    '<div class="alert alert-error">部署失败: ' + 
                    (message.data.error || message.data.result?.message || '未知错误') + '</div>';
                if (message.data.result?.buildOutput) {
                    document.getElementById('log-content').value = message.data.result.buildOutput;
                }
            }
            break;
    }
}

function renderProjectList(projects) {
    const listContainer = document.getElementById('project-list');
    
    if (projects.length === 0) {
        listContainer.innerHTML = 
            '<div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">' +
            '未找到WAR包装类型的Java Web项目<br>' +
            '<small style="opacity: 0.8; margin-top: 10px; display: block;">' +
            '请确保项目使用 &lt;packaging&gt;war&lt;/packaging&gt; 或应用了war插件' +
            '</small></div>';
        return;
    }
    
    const html = projects.map((project, index) => `
        <div class="project-item" data-index="${index}" onclick="selectProject(${index})">
            <div class="project-name">${project.name}</div>
            <div class="project-path">${project.path}</div>
            <div class="project-type">${project.type}</div>
        </div>
    `).join('');
    
    listContainer.innerHTML = html;
}
