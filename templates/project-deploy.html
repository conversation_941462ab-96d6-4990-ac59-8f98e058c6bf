<div class="header">
    <h1>📦 部署项目</h1>
    <p>将Java Web项目部署到Tomcat实例: <strong>{{instanceName}}</strong></p>
</div>

<div id="alert-container"></div>

<div class="tabs">
    <div class="tab active" data-tab="select-project">选择项目</div>
    <div class="tab" data-tab="configure-deployment">配置部署</div>
    <div class="tab" data-tab="deploy-progress">部署进度</div>
</div>

<div id="select-project" class="tab-content active">
    <div class="card">
        <div class="card-header">工作区项目</div>
        
        <div class="button-group" style="margin-top: 0; padding-top: 0; border-top: none;">
            <button type="button" class="button button-secondary" onclick="scanProjects()">
                🔍 扫描项目
            </button>
        </div>
        
        <div id="project-list" class="project-list">
            <div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">
                点击"扫描项目"来查找工作区中的Java Web项目<br>
                <small style="color: var(--vscode-descriptionForeground); opacity: 0.8;">
                    只会显示WAR包装类型的Web项目，自动过滤JAR包装的普通Java项目
                </small>
            </div>
        </div>
    </div>
</div>

<div id="configure-deployment" class="tab-content">
    <div class="card">
        <div class="card-header">部署配置</div>
        
        <div id="selected-project-info" style="display: none;">
            <div class="alert alert-success">
                <strong>已选择项目:</strong> <span id="selected-project-name"></span><br>
                <strong>项目路径:</strong> <span id="selected-project-path"></span><br>
                <strong>项目类型:</strong> <span id="selected-project-type"></span>
            </div>
        </div>
        
        <div class="form-group">
            <label for="contextPath">上下文路径</label>
            <select id="contextPath" name="contextPath">
                <option value="ROOT">ROOT (根路径部署)</option>
                <option value="custom">自定义路径</option>
            </select>
            <div class="help-text">选择应用的访问路径</div>
        </div>
        
        <div class="form-group" id="customContextPath" style="display: none;">
            <label for="customPath">自定义上下文路径</label>
            <input type="text" id="customPath" name="customPath" placeholder="/myapp">
            <div class="help-text">必须以"/"开头，例如: /myapp</div>
        </div>
        
        <div class="checkbox-group">
            <input type="checkbox" id="enableHotDeploy" name="enableHotDeploy" checked>
            <label for="enableHotDeploy">启用热部署</label>
        </div>
        
        <div id="hot-deploy-config" class="card" style="margin-top: 15px;">
            <div class="card-header">热部署配置</div>
            
            <div class="form-group">
                <label for="watchExtensions">监听文件扩展名</label>
                <input type="text" id="watchExtensions" name="watchExtensions" 
                       value="java,jsp,html,css,js,xml,properties">
                <div class="help-text">用逗号分隔，例如: java,jsp,html</div>
            </div>
            
            <div class="form-group">
                <label for="excludeDirectories">排除目录</label>
                <input type="text" id="excludeDirectories" name="excludeDirectories" 
                       value="target,build,node_modules,.git">
                <div class="help-text">用逗号分隔，例如: target,build</div>
            </div>
            
            <div class="form-group">
                <label for="deployDelay">部署延迟 (毫秒)</label>
                <input type="number" id="deployDelay" name="deployDelay" value="1000" min="0">
                <div class="help-text">文件变化后等待多长时间再部署</div>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="restartApp" name="restartApp">
                <label for="restartApp">文件变化时重启应用</label>
            </div>
        </div>
    </div>
</div>

<div id="deploy-progress" class="tab-content">
    <div class="card">
        <div class="card-header">部署进度</div>
        
        <div id="deploy-status">
            <div style="text-align: center; padding: 40px; color: var(--vscode-descriptionForeground);">
                配置完成后点击"开始部署"按钮
            </div>
        </div>
        
        <div id="deploy-log" style="display: none;">
            <div class="form-group">
                <label>部署日志</label>
                <textarea id="log-content" readonly style="height: 200px; font-family: monospace;"></textarea>
            </div>
        </div>
    </div>
</div>

<div class="loading">
    <div class="spinner"></div>
    <span id="loading-text">正在处理...</span>
</div>

<div class="button-group">
    <button type="button" id="prev-btn" class="button button-secondary" onclick="previousStep()" style="display: none;">
        上一步
    </button>
    <button type="button" id="next-btn" class="button button-primary" onclick="nextStep()" disabled>
        下一步
    </button>
    <button type="button" id="deploy-btn" class="button button-primary" onclick="startDeploy()" style="display: none;">
        开始部署
    </button>
    <button type="button" class="button button-secondary" onclick="window.close()">
        取消
    </button>
</div>
