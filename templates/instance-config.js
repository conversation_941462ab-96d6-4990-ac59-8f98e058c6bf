let originalConfig = null;
let instanceId = '{{instanceId}}';

function initPage() {
    // 浏览器类型变化处理
    document.getElementById('browserType').addEventListener('change', function() {
        const customPath = document.getElementById('customBrowserPath');
        if (this.value === 'custom') {
            customPath.style.display = 'block';
        } else {
            customPath.style.display = 'none';
        }
    });
    
    // 调试开关处理
    document.getElementById('enableDebug').addEventListener('change', function() {
        const debugPortGroup = document.getElementById('debugPortGroup');
        if (this.checked) {
            debugPortGroup.style.display = 'block';
        } else {
            debugPortGroup.style.display = 'none';
        }
    });
    
    // 端口变化时验证
    const portInputs = document.querySelectorAll('input[type="number"]');
    portInputs.forEach(input => {
        if (input.name.includes('Port')) {
            input.addEventListener('blur', validatePorts);
        }
    });
    
    // 表单提交
    document.getElementById('config-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveConfiguration();
    });
    
    // 加载实例配置
    loadInstanceConfiguration();
}

function loadInstanceConfiguration() {
    showLoading();
    document.getElementById('loading-text').textContent = '正在加载实例配置...';
    sendMessage('loadInstance', { instanceId: instanceId });
}

function saveConfiguration() {
    const formData = new FormData(document.getElementById('config-form'));
    
    const config = {
        instanceId: instanceId,
        name: formData.get('instanceName'),
        description: formData.get('description'),
        baseTomcatPath: formData.get('baseTomcatPath'),
        ports: {
            httpPort: parseInt(formData.get('httpPort')),
            httpsPort: parseInt(formData.get('httpsPort')),
            ajpPort: parseInt(formData.get('ajpPort')),
            jmxPort: parseInt(formData.get('jmxPort')),
            shutdownPort: parseInt(formData.get('shutdownPort'))
        },
        jvm: {
            jrePath: formData.get('jrePath') || '',
            minHeapSize: formData.get('minHeapSize'),
            maxHeapSize: formData.get('maxHeapSize'),
            permGenSize: formData.get('permGenSize'),
            metaspaceSize: formData.get('metaspaceSize'),
            additionalArgs: parseJvmArgs(formData.get('additionalJvmArgs'))
        },
        browser: {
            type: formData.get('browserType'),
            autoOpen: formData.has('autoOpenBrowser'),
            defaultPage: formData.get('defaultPage'),
            customPath: formData.get('customPath')
        },
        advanced: {
            startupTimeout: parseInt(formData.get('startupTimeout')),
            shutdownTimeout: parseInt(formData.get('shutdownTimeout')),
            enableJmx: formData.has('enableJmx'),
            enableDebug: formData.has('enableDebug'),
            debugPort: parseInt(formData.get('debugPort')) || 5005,
            workingDirectory: formData.get('workingDirectory')
        }
    };
    
    showLoading();
    document.getElementById('loading-text').textContent = '正在保存配置...';
    sendMessage('updateInstance', config);
}

function resetToOriginal() {
    if (originalConfig && confirm('确定要重置到原始配置吗？所有未保存的更改将丢失。')) {
        populateForm(originalConfig);
        showAlert('已重置到原始配置', 'success');
    }
}

function parseJvmArgs(argsText) {
    if (!argsText) return [];
    
    // 按行分割，然后按空格分割，过滤空字符串
    return argsText.split(/[\n\r]+/)
        .map(line => line.trim())
        .filter(line => line.length > 0)
        .flatMap(line => line.split(/\s+/))
        .filter(arg => arg.length > 0);
}

function formatJvmArgs(args) {
    if (!args || args.length === 0) return '';
    return args.join('\n');
}

function populateForm(instance) {
    // 保存原始配置
    originalConfig = JSON.parse(JSON.stringify(instance));
    
    // 基本信息
    document.getElementById('instanceName').value = instance.name || '';
    document.getElementById('description').value = instance.description || '';
    document.getElementById('baseTomcatPath').value = instance.baseTomcatPath || '';
    
    // 状态显示
    updateStatusDisplay(instance.status);
    
    // 端口配置
    if (instance.ports) {
        document.getElementById('httpPort').value = instance.ports.httpPort || 8080;
        document.getElementById('httpsPort').value = instance.ports.httpsPort || 8443;
        document.getElementById('ajpPort').value = instance.ports.ajpPort || 8009;
        document.getElementById('jmxPort').value = instance.ports.jmxPort || 9999;
        document.getElementById('shutdownPort').value = instance.ports.shutdownPort || 8005;
    }
    
    // JVM配置
    if (instance.jvm) {
        document.getElementById('jrePath').value = instance.jvm.jrePath || '';
        document.getElementById('minHeapSize').value = instance.jvm.minHeapSize || '';
        document.getElementById('maxHeapSize').value = instance.jvm.maxHeapSize || '';
        document.getElementById('permGenSize').value = instance.jvm.permGenSize || '';
        document.getElementById('metaspaceSize').value = instance.jvm.metaspaceSize || '';
        document.getElementById('additionalJvmArgs').value = formatJvmArgs(instance.jvm.additionalArgs);
    }
    
    // 浏览器配置
    if (instance.browser) {
        document.getElementById('browserType').value = instance.browser.type || 'default';
        document.getElementById('autoOpenBrowser').checked = instance.browser.autoOpen !== false;
        document.getElementById('defaultPage').value = instance.browser.defaultPage || '';
        document.getElementById('customPath').value = instance.browser.customPath || '';
        
        // 触发浏览器类型变化事件
        document.getElementById('browserType').dispatchEvent(new Event('change'));
    }
    
    // 高级配置
    if (instance.advanced) {
        document.getElementById('startupTimeout').value = instance.advanced.startupTimeout || 60;
        document.getElementById('shutdownTimeout').value = instance.advanced.shutdownTimeout || 30;
        document.getElementById('enableJmx').checked = instance.advanced.enableJmx || false;
        document.getElementById('enableDebug').checked = instance.advanced.enableDebug || false;
        document.getElementById('debugPort').value = instance.advanced.debugPort || 5005;
        document.getElementById('workingDirectory').value = instance.advanced.workingDirectory || '';
        
        // 触发调试开关变化事件
        document.getElementById('enableDebug').dispatchEvent(new Event('change'));
    }
}

function updateStatusDisplay(status) {
    const indicator = document.getElementById('status-indicator');
    const text = document.getElementById('status-text');
    
    indicator.className = 'status-indicator';
    
    switch (status) {
        case 'running':
            indicator.classList.add('status-running');
            text.textContent = '运行中';
            break;
        case 'stopped':
            indicator.classList.add('status-stopped');
            text.textContent = '已停止';
            break;
        case 'starting':
            indicator.classList.add('status-starting');
            text.textContent = '启动中';
            break;
        default:
            text.textContent = '未知';
    }
}

function validatePorts() {
    const ports = {
        httpPort: parseInt(document.getElementById('httpPort').value),
        httpsPort: parseInt(document.getElementById('httpsPort').value),
        ajpPort: parseInt(document.getElementById('ajpPort').value),
        jmxPort: parseInt(document.getElementById('jmxPort').value),
        shutdownPort: parseInt(document.getElementById('shutdownPort').value)
    };
    
    sendMessage('validatePorts', { ports, instanceId });
}

function suggestPorts() {
    sendMessage('suggestPorts', { instanceId });
}

function checkPortsAvailability() {
    const ports = {
        httpPort: parseInt(document.getElementById('httpPort').value),
        httpsPort: parseInt(document.getElementById('httpsPort').value),
        ajpPort: parseInt(document.getElementById('ajpPort').value),
        jmxPort: parseInt(document.getElementById('jmxPort').value),
        shutdownPort: parseInt(document.getElementById('shutdownPort').value)
    };
    
    showLoading();
    document.getElementById('loading-text').textContent = '正在检查端口可用性...';
    sendMessage('checkPortsAvailability', { ports, instanceId });
}

function refreshStatus() {
    sendMessage('refreshInstanceStatus', { instanceId });
}

function selectTomcatPath() {
    sendMessage('selectTomcatPath', {});
}

function selectJrePath() {
    sendMessage('selectJrePath', {});
}

function loadDefaultJrePath() {
    sendMessage('loadDefaultJrePath', {});
}

function selectBrowserPath() {
    sendMessage('selectBrowserPath', {});
}

function handleMessage(message) {
    switch (message.command) {
        case 'instanceLoaded':
            hideLoading();
            if (message.data.instance) {
                populateForm(message.data.instance);
            } else {
                showAlert('无法加载实例配置', 'error');
            }
            break;
            
        case 'instanceUpdated':
            hideLoading();
            if (message.data.success) {
                showAlert('配置保存成功！', 'success');
                // 重新加载配置以获取最新状态
                setTimeout(() => loadInstanceConfiguration(), 1000);
            } else {
                showAlert('保存配置失败: ' + message.data.error, 'error');
            }
            break;
            
        case 'portsValidated':
            const validation = document.getElementById('port-validation');
            if (message.data.errors && message.data.errors.length > 0) {
                validation.innerHTML = '<div class="alert alert-error">端口配置错误: ' + 
                    message.data.errors.join(', ') + '</div>';
            } else {
                validation.innerHTML = '<div class="alert alert-success">端口配置有效</div>';
            }
            break;
            
        case 'portsSuggested':
            if (message.data.ports) {
                const ports = message.data.ports;
                document.getElementById('httpPort').value = ports.httpPort;
                document.getElementById('httpsPort').value = ports.httpsPort;
                document.getElementById('ajpPort').value = ports.ajpPort;
                document.getElementById('jmxPort').value = ports.jmxPort;
                document.getElementById('shutdownPort').value = ports.shutdownPort;
                showAlert('已重新分配可用端口', 'success');
            }
            break;
            
        case 'portsAvailabilityChecked':
            hideLoading();
            if (message.data.results) {
                const results = message.data.results;
                let message_text = '端口可用性检查结果:\n';
                for (const [port, available] of Object.entries(results)) {
                    message_text += `${port}: ${available ? '可用' : '被占用'}\n`;
                }
                alert(message_text);
            }
            break;
            
        case 'instanceStatusRefreshed':
            if (message.data.status) {
                updateStatusDisplay(message.data.status);
                showAlert('状态已刷新', 'success');
            }
            break;
            
        case 'tomcatPathSelected':
            if (message.data.path) {
                document.getElementById('baseTomcatPath').value = message.data.path;
                showAlert('Tomcat路径已更新', 'success');
            }
            break;
            
        case 'jrePathSelected':
            if (message.data.path) {
                document.getElementById('jrePath').value = message.data.path;
                showAlert('JRE路径已选择', 'success');
            }
            break;
            
        case 'defaultJrePathLoaded':
            if (message.data.path) {
                document.getElementById('jrePath').value = message.data.path;
                showAlert('已加载默认JRE路径', 'success');
            } else {
                showAlert('未找到默认JRE路径', 'warning');
            }
            break;
            
        case 'browserPathSelected':
            if (message.data.path) {
                document.getElementById('customPath').value = message.data.path;
                showAlert('浏览器路径已选择', 'success');
            }
            break;
    }
}
