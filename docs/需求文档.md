# VSCode Tomcat 热部署插件需求文档

## 一、项目概述

### 1.1 项目背景

目前，Java 开发者在使用 VSCode 开发 Java Web 项目时，缺少类似 IntelliJ IDEA 中"Tomcat and TomEE"的插件，无法便捷地将项目热部署到 Tomcat 服务器。这导致开发效率低下，无法充分发挥 VSCode 的优势。为解决这一问题，特开发此插件，以满足 Java 开发者在 VSCode 中高效开发 Java Web 项目的需求。

### 1.2 项目目标

开发一款 VSCode 插件，实现类似 IntelliJ IDEA 中"Tomcat and TomEE"插件的功能，支持 Tomcat 服务器的管理（启动、停止、重启）以及 Java Web 项目的热部署，提高开发效率。

### 1.3 预期效果

- 插件成功安装并集成到 VSCode 中
- 能够在 VSCode 中配置和管理 Tomcat 服务器
- 实现 Java Web 项目的快速部署和热更新
- 提供友好的用户界面和操作体验

## 二、功能需求

### 2.1 Tomcat 服务器管理

- **基础 Tomcat 配置**

  - 支持选择一个基础 Tomcat 安装目录作为模板
  - 可基于选定的 Tomcat 创建多个独立的 Tomcat 服务器实例
  - 每个实例拥有独立的配置文件和工作目录
  - 支持添加、编辑和删除 Tomcat 服务器实例配置

- **多实例管理**

  - 支持基于一个 Tomcat 安装创建多个服务器实例
  - 每个实例可部署不同的 WAR 工程项目
  - 实例间相互独立，可单独启动、停止和配置
  - 支持为每个实例设置独立的名称和描述

- **端口配置**

  - **HTTP 端口**：可为每个 Tomcat 实例配置独立的 HTTP 端口（默认 8080）
  - **HTTPS 端口**：可配置 HTTPS 端口（默认 8443），支持 SSL 证书配置
  - **AJP 端口**：可配置 AJP 连接器端口（默认 8009）
  - **JMX 端口**：可配置 JMX 远程监控端口，便于性能监控和管理
  - **管理端口**：可配置 Tomcat 管理端口（默认 8005）
  - 端口冲突检测：自动检测端口占用情况，避免端口冲突

- **JRE 版本管理**

  - 支持为每个 Tomcat 实例指定不同的 JRE 版本
  - 自动检测系统中已安装的 JRE 版本
  - 支持手动指定 JRE 路径
  - 显示每个实例当前使用的 JRE 版本信息

- **生命周期管理**

  - 提供启动、停止、重启 Tomcat 服务器实例的功能
  - 在状态栏显示每个实例的状态（启动中、运行中、已停止、错误）
  - 支持批量操作，如同时启动多个服务器实例
  - 支持单独操作，可任意启动或停止指定的实例

- **日志查看**
  - 实时显示每个 Tomcat 实例的运行日志
  - 支持日志过滤（如按级别、关键词过滤）
  - 支持清空日志和保存日志到文件
  - 为每个实例提供独立的日志视图

### 2.2 项目部署与热更新

- **WAR 工程管理**

  - 支持识别和管理工作区中的多个 WAR 工程项目
  - 可将多个 WAR 工程部署到同一个 Tomcat 实例
  - 支持为每个 WAR 工程创建独立的 Tomcat 实例
  - 自动检测项目类型（Maven、Gradle、普通 Java Web 项目）

- **上下文路径配置**

  - **ROOT 部署**：支持将项目直接部署到 ROOT 上下文（默认选项）
  - **自定义上下文**：可为每个项目指定自定义的上下文路径（如 /myapp、/api 等）
  - **上下文路径验证**：检查上下文路径的合法性和唯一性
  - **URL 预览**：显示部署后的完整访问 URL

- **部署配置**

  - 支持为不同项目配置部署参数，如上下文路径、部署目录等
  - 可选择多个 Web 项目同时部署到同一 Tomcat 实例
  - 支持指定每个项目使用的 JRE 版本
  - 支持配置项目的启动优先级

- **一键部署**

  - 提供快速部署功能，将选中的项目部署到配置好的 Tomcat 服务器实例
  - 支持增量部署，只部署修改过的文件
  - 支持全量重新部署
  - 部署前自动构建项目（Maven/Gradle）

- **热更新**
  - 支持代码修改后的热部署，无需重启服务器即可看到效果
  - 支持配置热部署触发条件（如文件保存时自动部署）
  - 支持 JSP、静态资源的实时更新
  - 支持 Java 类的热替换（需要 JRE 支持）

### 2.3 浏览器集成与访问管理

- **浏览器配置**

  - 支持配置是否在 Tomcat 启动后自动打开浏览器
  - 可选择使用的浏览器类型：
    - 系统默认浏览器
    - Chrome/Chromium
    - Firefox
    - Safari（macOS）
    - Edge
    - 自定义浏览器路径
  - 支持为每个 Tomcat 实例单独配置浏览器设置

- **访问地址配置**

  - **默认首页**：可配置启动后跳转的默认地址（如 /index.html、/login 等）
  - **自定义路径**：支持为每个项目配置不同的启动页面
  - **完整 URL 构建**：自动根据 HTTP 端口、上下文路径和启动页面构建完整 URL
  - **HTTPS 支持**：当配置了 HTTPS 端口时，可选择使用 HTTPS 协议访问

- **快速访问功能**
  - 在状态栏或侧边栏提供"在浏览器中打开"按钮
  - 支持右键菜单快速打开项目页面
  - 提供 URL 复制功能，便于分享或在其他浏览器中打开
  - 支持同时在多个浏览器中打开进行测试

### 2.4 用户界面

- **侧边栏视图**

  - **层级结构显示**：
    - 基础 Tomcat 安装目录
    - 各个 Tomcat 实例（显示名称、状态、端口信息）
    - 每个实例下的部署应用（显示上下文路径、状态）
    - 项目配置信息
  - **状态指示器**：
    - 实例运行状态（绿色=运行中，红色=已停止，黄色=启动中，灰色=错误）
    - 端口占用状态显示
    - 部署应用的健康状态
  - **右键菜单操作**：
    - 启动/停止/重启 Tomcat 实例
    - 部署/取消部署项目
    - 在浏览器中打开应用
    - 查看实例配置
    - 复制访问 URL

- **命令面板**

  - 支持通过命令面板执行各种操作
  - 提供快速搜索和执行命令的功能
  - 支持模糊搜索 Tomcat 实例和项目名称
  - 快速切换和操作不同的实例

- **配置向导**

  - **基础 Tomcat 配置向导**：引导用户选择 Tomcat 安装目录
  - **实例创建向导**：帮助用户快速创建新的 Tomcat 实例
  - **项目部署向导**：引导用户配置项目部署参数
  - **浏览器配置向导**：帮助用户设置浏览器偏好

- **状态栏**

  - 显示当前活动的 Tomcat 实例数量
  - 显示部署进度和操作状态
  - 提供快速访问按钮（启动/停止所有实例）
  - 显示端口使用情况概览

- **设置面板**
  - **实例配置面板**：详细配置每个 Tomcat 实例的参数
  - **端口管理面板**：统一管理所有实例的端口配置
  - **浏览器设置面板**：配置浏览器偏好和启动选项
  - **JRE 管理面板**：管理和选择不同的 JRE 版本

### 2.5 配置管理

- **全局配置**

  - 支持全局默认配置，如基础 Tomcat 安装路径
  - 支持默认 JRE 版本设置
  - 默认端口范围配置（避免端口冲突）
  - 默认浏览器设置
  - 全局热部署配置

- **实例配置**

  - 每个 Tomcat 实例拥有独立的配置文件
  - 实例级别的端口配置（HTTP、HTTPS、AJP、JMX、管理端口）
  - 实例级别的 JRE 版本指定
  - 实例级别的浏览器和启动页面配置
  - 实例级别的 JVM 参数设置

- **项目配置**

  - 项目级别的上下文路径配置
  - 项目级别的部署参数
  - 项目级别的热部署设置
  - 项目与 Tomcat 实例的关联配置

- **工作区配置**
  - 支持为不同工作区单独配置 Tomcat 服务器和部署参数
  - 配置文件自动保存到工作区，方便团队协作
  - 支持配置文件的导入和导出
  - 支持配置模板，快速创建相似的实例配置

## 三、非功能需求

### 3.1 性能需求

- 部署操作响应迅速，不影响 VSCode 的正常使用
- 支持大型项目的快速部署
- 日志显示流畅，不会因为大量日志而卡顿

### 3.2 安全需求

- 敏感信息（如服务器配置、JRE 路径）加密存储
- 执行服务器操作时进行权限验证，确保操作安全
- 防止未授权访问和操作

### 3.3 兼容性需求

- 支持 Windows、MacOS 和 Linux 操作系统
- 兼容不同版本的 Tomcat 服务器（如 7.x、8.x、9.x、10.x）
- 与 VSCode 的 Java 开发工具链（如 Language Support for Java™ by Red Hat）良好集成
- 支持不同版本的 JRE（如 Java 8、11、17 等）

### 3.4 易用性需求

- 提供清晰的用户指引和帮助文档
- 操作界面简洁直观，易于理解和使用
- 错误提示明确，帮助用户快速定位和解决问题
- 支持中文和英文界面

## 四、接口需求

### 4.1 VSCode API

- 使用 VSCode 提供的 API 实现插件的基本功能，如命令注册、视图创建、配置管理等

### 4.2 Tomcat 管理接口

- 通过 Tomcat 的管理 API（如 Manager 应用）实现服务器状态查询、应用部署等操作
- 支持与不同版本的 Tomcat 服务器进行通信

### 4.3 文件系统接口

- 与文件系统交互，实现项目文件的读取、打包和部署
- 支持监控文件变化，触发热部署

### 4.4 JRE 检测接口

- 自动检测系统中安装的 JRE 版本
- 支持手动指定 JRE 路径

## 五、验收标准

### 5.1 功能验收

**基础功能验收**

- 能够成功选择和配置基础 Tomcat 安装目录
- 能够基于基础 Tomcat 创建多个独立的服务器实例
- 能够为每个实例设置独立的名称和描述

**多实例管理验收**

- 能够同时管理多个 Tomcat 实例（至少支持 5 个实例）
- 能够独立启动、停止、重启任意一个或多个实例
- 实例间相互独立，一个实例的操作不影响其他实例

**端口配置验收**

- 能够为每个实例配置独立的 HTTP、HTTPS、AJP、JMX 和管理端口
- 端口冲突检测功能正常工作，能够提示端口占用情况
- 端口配置修改后能够正确应用到实例

**项目部署验收**

- 能够将多个 WAR 工程部署到同一个 Tomcat 实例
- 能够为每个项目配置不同的上下文路径（包括 ROOT 部署）
- 能够为不同项目指定不同的 JRE 版本
- 能够将 Java Web 项目部署到 Tomcat 服务器并正常运行

**浏览器集成验收**

- 能够配置是否自动打开浏览器
- 能够选择不同类型的浏览器（系统默认、Chrome、Firefox 等）
- 能够配置启动后跳转的页面地址
- 能够正确构建和打开完整的访问 URL

**热更新验收**

- 修改代码后能够实现热更新，无需重启服务器
- 能够正确显示每个 Tomcat 实例的运行日志
- 日志视图能够区分不同实例的日志内容

### 5.2 性能验收

- 部署操作在合理时间内完成（如小型项目部署时间不超过 5 秒）
- 热更新操作响应迅速（不超过 3 秒）
- 日志显示无明显延迟

### 5.3 兼容性验收

- 在 Windows、MacOS 和 Linux 操作系统上均能正常工作
- 与主流版本的 Tomcat 服务器兼容
- 与 VSCode 的 Java 开发工具链无冲突
- 支持不同版本的 JRE

### 5.4 易用性验收

- 用户能够轻松完成插件的安装和配置
- 操作流程符合直觉，无需复杂的培训即可上手
- 错误提示清晰明了，帮助用户解决问题

## 六、术语表

- **热部署**：在不重启服务器的情况下，将修改后的代码更新到运行环境中
- **上下文路径**：Web 应用在服务器上的访问路径前缀，如 /myapp、/api 或 ROOT
- **JVM 参数**：Java 虚拟机的启动参数，用于配置内存、垃圾回收等
- **JRE**：Java 运行环境，包含 Java 虚拟机和运行 Java 程序所需的核心类库
- **WAR 文件**：Web 应用归档文件，包含 Web 应用的所有文件和目录结构
- **Tomcat 实例**：基于基础 Tomcat 安装创建的独立服务器实例，拥有独立的配置和端口
- **基础 Tomcat**：作为模板的 Tomcat 安装目录，用于创建多个实例
- **ROOT 部署**：将 Web 应用部署到 Tomcat 的根上下文，直接通过域名和端口访问
- **端口冲突**：多个服务尝试使用同一端口时发生的冲突，需要为每个实例配置不同端口
- **JMX 端口**：Java 管理扩展端口，用于远程监控和管理 Java 应用程序
- **AJP 端口**：Apache JServ Protocol 端口，用于 Web 服务器与 Tomcat 之间的通信
- **实例配置**：每个 Tomcat 实例的独立配置文件，包含端口、JRE、部署等设置
