# 🚀 Getting Started with Tomcat Manager

Welcome to Tomcat Manager for VSCode! This guide will help you get up and running quickly.

## 📋 Prerequisites

Before you begin, ensure you have:

- **VSCode** (version 1.60.0 or higher)
- **Java Development Kit** (JDK 8 or higher)
- **Apache Tomcat** (version 8.5 or higher)
- **Maven** or **Gradle** (for project building)
- **Java Extension Pack** for VSCode (for debugging support)

## 🔧 Installation

### Step 1: Install the Extension

1. Open VSCode
2. Go to Extensions (`Ctrl+Shift+X`)
3. Search for "Tomcat Manager"
4. Click **Install**

### Step 2: Install Java Extension Pack

1. Search for "Extension Pack for Java" in Extensions
2. Click **Install** (required for debugging features)

## 🏗️ Creating Your First Tomcat Instance

### Step 1: Open Command Palette

Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)

### Step 2: Create Instance

1. Type "Tomcat Manager: Create Instance"
2. Select the command
3. Follow the setup wizard:
   - **Instance Name**: Enter a descriptive name (e.g., "Development Server")
   - **Tomcat Path**: Browse to your Tomcat installation directory
   - **JRE Path**: Select your Java installation (auto-detected)
   - **Ports**: Use default ports or customize as needed

### Step 3: Verify Creation

- Check the **Tomcat Explorer** panel in the sidebar
- Your new instance should appear with a "stopped" status

## 📦 Deploying Your First Project

### Step 1: Prepare Your Project

Ensure your project has the correct structure:

```
your-project/
├── pom.xml (Maven) or build.gradle (Gradle)
├── src/
│   └── main/
│       ├── java/
│       └── webapp/
└── target/ (will be created after build)
```

### Step 2: Deploy Project

1. **Right-click** on your project folder in VSCode Explorer
2. Select **"Deploy to Tomcat"**
3. Choose your Tomcat instance
4. Configure deployment settings:
   - **Context Path**: `/` for root deployment or `/myapp` for custom path
   - **Build Command**: Auto-detected based on project type
   - **Output Directory**: Usually `target` for Maven, `build/libs` for Gradle

### Step 3: Start the Instance

1. In **Tomcat Explorer**, find your instance
2. Click the **▶️ Start** button
3. Wait for the startup to complete
4. Look for "✅ Tomcat启动成功！" in the output

## 🌐 Accessing Your Application

### Automatic Browser Launch

If auto-launch is enabled, your default browser will open automatically.

### Manual Access

Open your browser and navigate to:
- **Root deployment**: `http://localhost:8080/`
- **Custom path**: `http://localhost:8080/your-context-path/`

## 🐛 Your First Debug Session

### Step 1: Set Breakpoints

1. Open your Java source file
2. Click on the left margin next to line numbers
3. Red dots (●) indicate active breakpoints

### Step 2: Start Debug Mode

1. In **Tomcat Explorer**, click the **🐛 Debug** button
2. Wait for the debug startup message
3. Note the debug port (usually 5005)

### Step 3: Connect Debugger

1. Press **F5** to start debugging
2. If prompted, select the auto-generated debug configuration
3. Look for the debug toolbar at the top of VSCode

### Step 4: Trigger Breakpoints

1. Access your web application in the browser
2. Navigate to pages that execute your breakpoint code
3. VSCode should pause at breakpoints, allowing you to:
   - Inspect variables
   - Step through code
   - Evaluate expressions

## 🚀 Hot Deployment

### Automatic File Watching

Once your application is running:

1. **Edit static files** (CSS, JS, HTML) - Changes deploy instantly
2. **Edit Java files** - Triggers automatic rebuild and redeploy
3. **Edit configuration files** - Triggers full redeploy

### Manual Redeploy

- Right-click instance → **"Redeploy"**
- Or use Command Palette: "Tomcat Manager: Redeploy"

## 🔧 Common Configuration

### Port Conflicts

If you encounter port conflicts:

1. Right-click instance → **"Configure"**
2. Go to **"Ports"** tab
3. Modify conflicting ports
4. Save and restart instance

### Memory Settings

For better performance:

1. Right-click instance → **"Configure"**
2. Go to **"JVM"** tab
3. Adjust memory settings:
   - **Min Heap**: 256m (minimum)
   - **Max Heap**: 512m or higher for large applications

### Browser Settings

To customize browser behavior:

1. Right-click instance → **"Configure"**
2. Go to **"Browser"** tab
3. Configure:
   - **Auto Launch**: Enable/disable automatic browser opening
   - **Default Browser**: Choose your preferred browser
   - **Default Page**: Set custom landing page

## 🎯 Next Steps

Now that you have the basics working:

1. **[Debug Tutorial](debug-tutorial.md)** - Learn advanced debugging techniques
2. **[Multi-Module Tutorial](multi-module-tutorial.md)** - Work with complex projects
3. **[Hot Deployment Guide](hot-deployment-tutorial.md)** - Master hot deployment features

## 🆘 Need Help?

- **[Troubleshooting Guide](../troubleshooting.md)** - Common issues and solutions
- **[Configuration Reference](../configuration.md)** - Detailed configuration options
- **[GitHub Issues](https://github.com/your-repo/issues)** - Report bugs or request features

---

**Happy coding! 🎉**
