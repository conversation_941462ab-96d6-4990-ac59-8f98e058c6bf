# 🐛 Tomcat Manager 调试教程

通过这个全面的教程掌握Tomcat Manager的调试功能。

## 🎯 您将学到

- 一键设置调试模式
- 理解自动调试配置
- 高级断点技巧
- 调试多模块项目
- 排查常见调试问题

## 📋 前置要求

- 已安装Tomcat Manager扩展
- VSCode的Java Extension Pack
- Java Web项目 (Maven或Gradle)
- Java调试概念的基础理解

## 🚀 快速调试设置

### 步骤1：一键启动调试

1. **打开VSCode侧边栏的Tomcat Explorer**
2. **在列表中找到您的实例**
3. **点击🐛调试按钮** (在▶️启动按钮旁边)
4. **等待启动** - 您将在输出中看到调试信息

### 步骤2：自动配置

扩展会自动：
- ✅ **检测Java版本** 并配置JDWP参数
- ✅ **分配调试端口** (通常是5005，如果被占用会自动递增)
- ✅ **创建launch.json** 包含正确的调试配置
- ✅ **升级旧实例** 以支持调试功能

### 步骤3：连接调试器

1. **按F5** 开始调试
2. **选择自动生成的配置** (如果提示)
3. **查看调试工具栏** 在VSCode顶部
4. **验证连接** - 断点应该变成实心红点

## 🎯 设置有效的断点

### 基础断点

```java
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        // 👈 点击这里设置基础断点
        User user = userService.findById(id);
        
        if (user == null) {
            // 👈 另一个好的断点位置
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(user);
    }
}
```

### 条件断点

右键点击断点 → **编辑断点** → **表达式**

```java
public void processUsers(List<User> users) {
    for (User user : users) {
        // 条件: user.getId() == 123
        processUser(user); // 👈 只有当用户ID是123时才中断
    }
}
```

### 日志断点

右键点击断点 → **编辑断点** → **日志消息**

```java
public String calculateResult(int value) {
    // 日志消息: "Processing value: {value}"
    return "Result: " + (value * 2); // 👈 记录日志但不停止
}
```

## 🏗️ 调试多模块项目

### 项目结构示例

```
parent-project/
├── pom.xml
├── core/
│   ├── pom.xml
│   └── src/main/java/com/example/core/
├── service/
│   ├── pom.xml
│   └── src/main/java/com/example/service/
└── web/
    ├── pom.xml
    └── src/main/java/com/example/web/
```

### 跨模块调试

```java
// 在web模块 - Controller
@RestController
public class ApiController {
    
    @Autowired
    private BusinessService businessService; // 来自service模块
    
    @PostMapping("/api/process")
    public ResponseEntity<String> process(@RequestBody String data) {
        // 👈 在这里设置断点
        String result = businessService.processData(data);
        return ResponseEntity.ok(result);
    }
}

// 在service模块 - Service
@Service
public class BusinessService {
    
    @Autowired
    private CoreProcessor coreProcessor; // 来自core模块
    
    public String processData(String data) {
        // 👈 这里也设置断点
        return coreProcessor.process(data);
    }
}

// 在core模块 - 核心逻辑
@Component
public class CoreProcessor {
    
    public String process(String input) {
        // 👈 这里也设置断点以完整调试流程
        return "Processed: " + input.toUpperCase();
    }
}
```

### 调试流程

1. **在所有要调试的模块中设置断点**
2. **从Tomcat Explorer启动调试模式**
3. **连接调试器** (F5)
4. **进行触发流程的API调用**
5. **按顺序单步执行** 每个模块

## 🔧 高级调试技巧

### 变量检查

在断点处停止时：

```java
public void complexMethod(User user, List<Order> orders) {
    // 在这里停止时，您可以检查：
    // - user对象及其所有属性
    // - orders列表和单个order对象
    // - 作用域内的局部变量
    // - 方法参数
}
```

**使用的调试面板：**
- **变量**: 查看所有局部变量及其值
- **监视**: 添加自定义表达式进行监控
- **调用堆栈**: 查看方法调用层次结构

### 表达式求值

在 **调试控制台** 中，您可以求值表达式：

```java
// 在调试时，在调试控制台中输入：
user.getName()
orders.size()
orders.stream().filter(o -> o.getStatus() == OrderStatus.PENDING).count()
```

### 步进控制

- **继续 (F5)**: 运行到下一个断点
- **单步跳过 (F10)**: 执行当前行，不进入方法
- **单步进入 (F11)**: 进入方法调用
- **单步跳出 (Shift+F11)**: 退出当前方法
- **重启**: 重启调试会话
- **停止**: 结束调试会话

## 🌐 Web应用程序调试场景

### 调试REST API

```java
@RestController
public class ProductController {
    
    @PostMapping("/products")
    public ResponseEntity<Product> createProduct(@RequestBody ProductDto dto) {
        // 👈 断点：检查传入的DTO
        
        Product product = productService.create(dto);
        // 👈 断点：检查创建的产品
        
        return ResponseEntity.ok(product);
    }
}
```

**测试工具：**
- 浏览器开发者工具
- Postman
- curl命令
- VSCode REST Client扩展

### 调试表单提交

```java
@Controller
public class UserFormController {
    
    @PostMapping("/users/create")
    public String createUser(@ModelAttribute UserForm form, Model model) {
        // 👈 断点：检查表单数据
        
        if (form.hasErrors()) {
            // 👈 断点：调试验证错误
            return "user-form";
        }
        
        userService.save(form.toUser());
        return "redirect:/users";
    }
}
```

### 调试JSP/Thymeleaf

```java
@Controller
public class PageController {
    
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        List<Widget> widgets = widgetService.getActiveWidgets();
        // 👈 断点：在渲染前检查widget数据
        
        model.addAttribute("widgets", widgets);
        return "dashboard"; // JSP/Thymeleaf模板
    }
}
```

## 🔍 调试问题排查

### 问题1：调试器无法连接

**症状：**
- "连接被拒绝"错误
- 断点保持空心圆圈

**解决方案：**
1. **检查调试端口** 在Tomcat启动日志中
2. **验证Tomcat以调试模式启动** (🐛按钮，不是▶️)
3. **检查防火墙** 没有阻止5005端口
4. **重启VSCode** 再试一次

### 问题2：调试class文件而不是源码

**症状：**
- 调试器打开.class文件而不是.java文件
- 变量名被混淆

**解决方案：**
1. **确保调试编译**：
   ```xml
   <!-- Maven -->
   <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-compiler-plugin</artifactId>
       <configuration>
           <debug>true</debug>
           <debuglevel>lines,vars,source</debuglevel>
       </configuration>
   </plugin>
   ```

2. **重新构建项目**: `mvn clean compile`
3. **重新部署** 到Tomcat

### 问题3：断点未命中

**可能原因：**
- 请求未执行到该代码
- 断点在不可执行行上
- 源码/编译代码不匹配

**解决方案：**
1. **验证请求路径** 匹配控制器映射
2. **移动断点** 到可执行代码行
3. **清理并重新构建** 项目

## 🎯 最佳实践

### 1. 策略性断点放置

```java
@Service
public class OrderService {
    
    public Order processOrder(OrderRequest request) {
        // ✅ 好：方法入口点
        log.info("Processing order: {}", request.getId());
        
        Order order = validateOrder(request);
        // ✅ 好：验证后
        
        if (order.requiresApproval()) {
            // ✅ 好：条件逻辑
            return sendForApproval(order);
        }
        
        return completeOrder(order);
        // ✅ 好：返回前
    }
}
```

### 2. 明智使用条件断点

```java
// 不要在每次迭代都中断：
for (int i = 0; i < 1000; i++) {
    processItem(i); // ❌ 不要无条件在这里中断
}

// 使用条件：i == 999 或 i % 100 == 0
```

### 3. 利用日志断点

```java
public void batchProcess(List<Item> items) {
    // 日志消息: "Processing batch of {items.size()} items"
    for (Item item : items) {
        // 日志消息: "Processing item {item.getId()}"
        process(item);
    }
}
```

## 🚀 下一步

- **[多模块项目教程](multi-module-tutorial-zh.md)** - 高级项目结构
- **[热部署指南](hot-deployment-tutorial-zh.md)** - 高效开发工作流
- **[技术文档](../TOMCAT_DEBUG_SUPPORT.md)** - 实现细节

## 🆘 需要帮助？

- **[调试问题修复](../DEBUG_JDWP_TRANSPORT_FIX.md)** - 常见调试问题
- **[GitHub Issues](https://github.com/your-repo/issues)** - 报告调试相关错误

---

**祝您调试愉快！ 🐛✨**
