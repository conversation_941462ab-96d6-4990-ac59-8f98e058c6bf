# 🏗️ Multi-Module Projects Tutorial

Learn how to work efficiently with Maven multi-module projects using Tomcat Manager.

## 🎯 What You'll Learn

- Setting up multi-module projects for Tomcat deployment
- Understanding automatic dependency resolution
- Optimizing build performance for large projects
- Debugging across multiple modules
- Best practices for complex project structures

## 📋 Prerequisites

- Tomcat Manager extension installed
- Maven knowledge (intermediate level)
- Understanding of Java project structures
- Basic knowledge of dependency management

## 🏗️ Understanding Multi-Module Projects

### Typical Project Structure

```
enterprise-app/
├── pom.xml                    # Parent POM
├── common/                    # Shared utilities
│   ├── pom.xml
│   └── src/main/java/
├── core/                      # Business logic
│   ├── pom.xml
│   └── src/main/java/
├── service/                   # Service layer
│   ├── pom.xml
│   └── src/main/java/
├── api/                       # REST API
│   ├── pom.xml
│   └── src/main/java/
└── web/                       # Web application (WAR)
    ├── pom.xml
    ├── src/main/java/
    └── src/main/webapp/
```

### Dependency Flow

```
web → api → service → core → common
```

## 🚀 Setting Up Your Multi-Module Project

### Step 1: Parent POM Configuration

```xml
<!-- enterprise-app/pom.xml -->
<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>enterprise-app</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    
    <modules>
        <module>common</module>
        <module>core</module>
        <module>service</module>
        <module>api</module>
        <module>web</module>
    </modules>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- Define versions for all modules -->
            <dependency>
                <groupId>com.example</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- ... other module dependencies -->
        </dependencies>
    </dependencyManagement>
</project>
```

### Step 2: Web Module Configuration

```xml
<!-- web/pom.xml -->
<project>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>enterprise-app</artifactId>
        <version>1.0.0</version>
    </parent>
    
    <artifactId>web</artifactId>
    <packaging>war</packaging>
    
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>service</artifactId>
        </dependency>
        <!-- Other dependencies -->
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

## 📦 Deploying Multi-Module Projects

### Step 1: Open Project in VSCode

1. **Open the parent directory** containing the parent POM
2. **Wait for Java extension** to recognize the project structure
3. **Verify module recognition** in the Java Projects view

### Step 2: Deploy Web Module

1. **Right-click on the web module folder**
2. **Select "Deploy to Tomcat"**
3. **Choose your Tomcat instance**
4. **Configure deployment settings:**
   - **Module to Deploy**: Select the web module
   - **Context Path**: `/` or `/your-app`
   - **Build Strategy**: Automatic (optimized for multi-module)

### Step 3: Automatic Dependency Resolution

The extension automatically:
- ✅ **Detects module dependencies** from POM files
- ✅ **Builds modules in correct order** (dependency-first)
- ✅ **Skips unchanged modules** for faster builds
- ✅ **Handles transitive dependencies** correctly

## ⚡ Build Optimization Features

### Intelligent Build Strategy

```
For projects with 10+ modules:
├── Dependency Analysis ✅
├── Incremental Building ✅
├── Parallel Compilation ✅
└── Smart Caching ✅
```

### Build Process Flow

```
1. Analyze module dependencies
2. Check for changes since last build
3. Build only changed modules + dependents
4. Use parallel compilation where possible
5. Package web module with all dependencies
```

### Performance Comparison

| Project Size | Traditional Build | Optimized Build | Time Saved |
|--------------|-------------------|-----------------|------------|
| 5 modules    | 45 seconds       | 25 seconds      | 44%        |
| 10 modules   | 2.5 minutes      | 1 minute        | 60%        |
| 20+ modules  | 8 minutes        | 2.5 minutes     | 69%        |

## 🐛 Debugging Multi-Module Projects

### Cross-Module Debugging Setup

1. **Set breakpoints** in multiple modules:

```java
// In web/src/main/java/com/example/web/
@RestController
public class OrderController {
    
    @PostMapping("/orders")
    public ResponseEntity<Order> createOrder(@RequestBody OrderDto dto) {
        // 👈 Breakpoint 1: Web layer
        return orderService.createOrder(dto);
    }
}

// In service/src/main/java/com/example/service/
@Service
public class OrderService {
    
    public ResponseEntity<Order> createOrder(OrderDto dto) {
        // 👈 Breakpoint 2: Service layer
        Order order = orderProcessor.process(dto);
        return ResponseEntity.ok(order);
    }
}

// In core/src/main/java/com/example/core/
@Component
public class OrderProcessor {
    
    public Order process(OrderDto dto) {
        // 👈 Breakpoint 3: Core logic
        return new Order(dto.getCustomerId(), dto.getItems());
    }
}
```

### Debug Configuration

The extension automatically creates a launch.json that supports:
- **Source path mapping** for all modules
- **Classpath inclusion** of all dependencies
- **Symbol resolution** across module boundaries

## 🔧 Advanced Configuration

### Custom Build Commands

For complex build requirements:

```json
// In .vscode/settings.json
{
    "tomcatManager.buildCommands": {
        "maven": {
            "clean": "mvn clean",
            "compile": "mvn compile -T 4", // Parallel compilation
            "package": "mvn package -DskipTests -T 4",
            "install": "mvn install -DskipTests -T 4"
        }
    }
}
```

### Module-Specific Settings

```json
{
    "tomcatManager.multiModule": {
        "buildOrder": ["common", "core", "service", "api", "web"],
        "parallelBuild": true,
        "incrementalBuild": true,
        "skipTests": true
    }
}
```

## 🚀 Hot Deployment with Multi-Module

### Automatic Change Detection

The extension monitors:
- **Java files** in all modules
- **Resource files** (properties, XML, etc.)
- **Web resources** (JSP, HTML, CSS, JS)
- **POM files** for dependency changes

### Smart Rebuild Strategy

```
Change in common module → Rebuild: common, core, service, api, web
Change in core module   → Rebuild: core, service, api, web
Change in service module → Rebuild: service, api, web
Change in web module    → Rebuild: web only
```

### Hot Deployment Flow

1. **File change detected** in any module
2. **Determine affected modules** based on dependencies
3. **Rebuild only necessary modules** in correct order
4. **Redeploy web application** with updated dependencies
5. **Preserve application state** where possible

## 🎯 Best Practices

### 1. Project Structure

```
✅ Good: Clear separation of concerns
enterprise-app/
├── domain/          # Domain models
├── infrastructure/  # Data access, external services
├── application/     # Use cases, application services
├── interfaces/      # Controllers, DTOs
└── web/            # Web configuration, static resources

❌ Avoid: Circular dependencies
service → core → service (circular!)
```

### 2. Dependency Management

```xml
<!-- ✅ Good: Use dependencyManagement in parent -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-bom</artifactId>
            <version>5.3.21</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>

<!-- ❌ Avoid: Version conflicts between modules -->
```

### 3. Build Performance

```xml
<!-- ✅ Good: Optimize compiler plugin -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <configuration>
        <fork>true</fork>
        <meminitial>128m</meminitial>
        <maxmem>512m</maxmem>
    </configuration>
</plugin>
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Module Not Found
**Problem**: "Module X not found during build"
**Solution**: 
- Check module is listed in parent POM
- Verify module directory structure
- Run `mvn clean install` from parent directory

#### 2. Circular Dependencies
**Problem**: Build fails with circular dependency error
**Solution**:
- Analyze dependency graph: `mvn dependency:tree`
- Refactor to remove circular references
- Consider extracting common interfaces

#### 3. Slow Build Performance
**Problem**: Build takes too long for large projects
**Solution**:
- Enable parallel building: `-T 4`
- Use incremental compilation
- Skip tests during development: `-DskipTests`

### Debug Issues

#### Source Code Not Found
**Problem**: Debugger shows compiled classes instead of source
**Solution**:
1. Ensure all modules compile with debug info
2. Check source paths in launch.json
3. Rebuild all modules: `mvn clean compile`

## 🚀 Next Steps

- **[Hot Deployment Tutorial](hot-deployment-tutorial.md)** - Optimize your development workflow
- **[Debug Tutorial](debug-tutorial.md)** - Master debugging techniques
- **[Technical Documentation](../MAVEN_MULTI_MODULE_SUPPORT.md)** - Implementation details

## 🆘 Need Help?

- **[Build Optimization Guide](../LARGE_PROJECT_BUILD_OPTIMIZATION.md)** - Performance tuning
- **[GitHub Issues](https://github.com/your-repo/issues)** - Report multi-module issues

---

**Build efficiently! 🏗️✨**
