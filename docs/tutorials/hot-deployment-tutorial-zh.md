# 🚀 热部署教程

掌握Tomcat Manager的热部署功能，实现高效的开发工作流。

## 🎯 您将学到

- 理解不同类型的热部署
- 配置自动文件监控
- 针对不同文件类型优化部署
- 排查热部署问题
- 开发工作流的最佳实践

## 📋 前置要求

- 已安装Tomcat Manager扩展
- 已部署的Java Web项目
- 理解Web应用程序结构的基础知识
- 熟悉Maven/Gradle构建过程

## 🔥 理解热部署

### 什么是热部署？

热部署允许您在不重启服务器的情况下更新正在运行的应用程序，提供：
- **更快的开发周期**
- **保持应用程序状态**
- **对更改的即时反馈**
- **提高生产力**

### 热部署类型

#### 1. 静态资源更新 (即时)
- **CSS文件** - 样式更改
- **JavaScript文件** - 客户端逻辑
- **HTML/JSP文件** - 模板更改
- **图像和资源** - 静态内容

#### 2. 配置更新 (快速)
- **Properties文件** - 应用程序配置
- **XML配置** - Spring, Hibernate配置
- **资源包** - 国际化

#### 3. Java代码更新 (需要重建)
- **Controller类** - 请求处理
- **Service类** - 业务逻辑
- **Entity类** - 数据模型
- **工具类** - 辅助函数

## 🚀 设置热部署

### 步骤1：启用热部署

1. **在Tomcat Explorer中右键点击您的Tomcat实例**
2. **选择"配置"**
3. **转到"热部署"选项卡**
4. **启用以下选项：**
   - ✅ **启用热部署**
   - ✅ **监控文件更改**
   - ✅ **Java更改时自动重建**
   - ✅ **保持会话状态**

### 步骤2：配置文件监控

```json
// 在 .vscode/settings.json 中
{
    "tomcatManager.hotDeploy": {
        "enabled": true,
        "watchPatterns": [
            "src/main/webapp/**/*",
            "src/main/resources/**/*",
            "src/main/java/**/*.java"
        ],
        "excludePatterns": [
            "**/target/**",
            "**/node_modules/**",
            "**/.git/**"
        ],
        "debounceDelay": 1000
    }
}
```

### 步骤3：启动热部署

1. **将项目部署到Tomcat**
2. **启动实例** (▶️ 启动按钮)
3. **验证热部署已激活** - 在日志中查找"🔥 热部署已启用"

## 📁 文件类型特定行为

### 静态资源 (即时更新)

```
src/main/webapp/
├── css/
│   └── styles.css      ← 更改立即部署
├── js/
│   └── app.js          ← 更改立即部署
├── images/
│   └── logo.png        ← 更改立即部署
└── WEB-INF/
    └── views/
        └── index.jsp   ← 更改立即部署
```

**示例工作流：**
1. **编辑CSS文件** - 更改背景颜色
2. **保存文件** (Ctrl+S)
3. **刷新浏览器** - 立即看到更改

### 配置文件 (快速更新)

```
src/main/resources/
├── application.properties  ← 触发上下文重新加载
├── logback.xml             ← 触发日志记录重新配置
└── messages.properties     ← 触发资源包重新加载
```

**示例工作流：**
1. **编辑application.properties** - 更改数据库URL
2. **保存文件** - 触发自动重新加载
3. **应用程序使用新配置重启**

### Java源文件 (重建 + 部署)

```
src/main/java/
├── controllers/
│   └── UserController.java    ← 触发重建 + 重新部署
├── services/
│   └── UserService.java       ← 触发重建 + 重新部署
└── models/
    └── User.java              ← 触发重建 + 重新部署
```

**示例工作流：**
1. **编辑Java文件** - 添加新方法
2. **保存文件** - 触发自动构建
3. **等待构建完成** - 在输出中查看进度
4. **应用程序使用新代码重新部署**

## 🔧 高级配置

### 自定义构建命令

```json
{
    "tomcatManager.hotDeploy": {
        "buildCommands": {
            "java": "mvn compile",
            "resources": "mvn process-resources",
            "full": "mvn package -DskipTests"
        }
    }
}
```

### 选择性文件监控

```json
{
    "tomcatManager.hotDeploy": {
        "fileTypes": {
            "static": {
                "patterns": ["**/*.css", "**/*.js", "**/*.html"],
                "action": "copy",
                "delay": 0
            },
            "java": {
                "patterns": ["**/*.java"],
                "action": "rebuild",
                "delay": 2000
            },
            "config": {
                "patterns": ["**/*.properties", "**/*.xml"],
                "action": "reload",
                "delay": 1000
            }
        }
    }
}
```

### 性能优化

```json
{
    "tomcatManager.hotDeploy": {
        "performance": {
            "batchChanges": true,
            "batchDelay": 3000,
            "parallelBuild": true,
            "incrementalBuild": true,
            "skipTests": true
        }
    }
}
```

## 🌐 实际场景

### 场景1：前端开发

**任务**: 更新CSS和JavaScript以改进UI

```css
/* styles.css - 之前 */
.header {
    background-color: #333;
    color: white;
}

/* styles.css - 之后 */
.header {
    background-color: #007bff;  /* 改为蓝色 */
    color: white;
    border-radius: 5px;         /* 添加圆角 */
}
```

**工作流：**
1. **在VSCode中编辑CSS文件**
2. **保存文件** (Ctrl+S)
3. **切换到浏览器**并刷新 (F5)
4. **立即看到更改** - 无需构建

### 场景2：后端API开发

**任务**: 添加新的REST端点

```java
@RestController
public class ProductController {
    
    // 现有端点
    @GetMapping("/products")
    public List<Product> getAllProducts() {
        return productService.findAll();
    }
    
    // 新端点 - 开发期间添加
    @PostMapping("/products")
    public ResponseEntity<Product> createProduct(@RequestBody Product product) {
        Product saved = productService.save(product);
        return ResponseEntity.ok(saved);
    }
}
```

**工作流：**
1. **向控制器添加新方法**
2. **保存文件** - 触发自动构建
3. **等待"构建完成"消息**
4. **使用Postman/浏览器测试新端点**

### 场景3：配置更改

**任务**: 更新数据库连接设置

```properties
# application.properties - 之前
spring.datasource.url=**********************************
spring.datasource.username=testuser

# application.properties - 之后
spring.datasource.url=**********************************
spring.datasource.username=produser
spring.datasource.password=newpassword
```

**工作流：**
1. **编辑properties文件**
2. **保存文件** - 触发上下文重新加载
3. **应用程序使用新数据库连接重启**
4. **在应用程序日志中验证连接**

## 🔍 监控热部署

### 输出面板消息

```
🔥 实例热部署已启用: my-app
📁 监控文件: /project/src/main/webapp
📁 监控文件: /project/src/main/java
📁 监控文件: /project/src/main/resources

📝 文件已更改: src/main/webapp/css/styles.css
✅ 静态资源已更新: styles.css

📝 文件已更改: src/main/java/com/example/UserController.java
🔨 正在构建项目...
✅ 构建完成，耗时 3.2秒
🚀 应用程序重新部署成功
```

### 状态指示器

- **🔥 热部署激活** - 文件监控已启用
- **📁 监控中** - 正在监控文件更改
- **🔨 构建中** - 编译进行中
- **🚀 部署中** - 应用程序更新进行中
- **✅ 就绪** - 应用程序准备接受请求

## 🐛 故障排除

### 问题1：未检测到更改

**症状：**
- 文件更改不触发部署
- 输出面板中没有消息

**解决方案：**
1. **检查文件监控是否启用**：
   ```json
   {
       "tomcatManager.hotDeploy.enabled": true
   }
   ```

2. **验证文件模式**：
   ```json
   {
       "tomcatManager.hotDeploy.watchPatterns": [
           "src/main/webapp/**/*",
           "src/main/java/**/*.java"
       ]
   }
   ```

3. **检查文件未被排除**：
   ```json
   {
       "tomcatManager.hotDeploy.excludePatterns": [
           "**/target/**"
       ]
   }
   ```

### 问题2：构建失败

**症状：**
- 热部署触发但失败
- 输出中有编译错误

**解决方案：**
1. **检查Java语法** - 修复编译错误
2. **验证依赖项** - 确保所有必需的JAR可用
3. **清理并重建**：
   ```bash
   mvn clean compile
   ```

### 问题3：性能慢

**症状：**
- 热部署时间过长
- 单个更改触发多次重建

**解决方案：**
1. **启用批处理**：
   ```json
   {
       "tomcatManager.hotDeploy.batchChanges": true,
       "tomcatManager.hotDeploy.batchDelay": 3000
   }
   ```

2. **使用增量构建**：
   ```json
   {
       "tomcatManager.hotDeploy.incrementalBuild": true
   }
   ```

3. **开发期间跳过测试**：
   ```json
   {
       "tomcatManager.hotDeploy.skipTests": true
   }
   ```

## 🎯 最佳实践

### 1. 文件组织

```
✅ 好：分离静态和动态内容
src/main/
├── webapp/
│   ├── static/     # CSS, JS, 图像 (即时更新)
│   └── WEB-INF/    # JSP, 配置 (快速更新)
└── java/           # Java代码 (需要重建)

❌ 避免：同一目录中混合文件类型
```

### 2. 开发工作流

```
✅ 好：增量开发
1. 启动带热部署的Tomcat
2. 进行小的、专注的更改
3. 每次更改后立即测试
4. 频繁提交工作更改

❌ 避免：大批量更改
```

### 3. 性能优化

```
✅ 好：为开发优化
- 启用增量构建
- 开发期间跳过测试
- 使用适当延迟的文件监控
- 批处理多个更改

❌ 避免：为小更改进行完整重建
```

## 🚀 下一步

- **[调试教程](debug-tutorial-zh.md)** - 将热部署与调试结合
- **[多模块项目教程](multi-module-tutorial-zh.md)** - 复杂项目的热部署
- **[技术文档](../HOT_DEPLOY_IMPLEMENTATION.md)** - 实现细节

## 🆘 需要帮助？

- **[热部署错误处理](../HOT_DEPLOY_ERROR_HANDLING_FIX.md)** - 常见问题和修复
- **[GitHub Issues](https://github.com/your-repo/issues)** - 报告热部署问题

---

**快速部署，更快开发！ 🚀🔥**
