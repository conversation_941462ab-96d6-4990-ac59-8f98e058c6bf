# 🏗️ 多模块项目教程

学习如何使用Tomcat Manager高效处理Maven多模块项目。

## 🎯 您将学到

- 为Tomcat部署设置多模块项目
- 理解自动依赖解析
- 优化大型项目的构建性能
- 跨多个模块进行调试
- 复杂项目结构的最佳实践

## 📋 前置要求

- 已安装Tomcat Manager扩展
- Maven知识 (中级水平)
- 理解Java项目结构
- 依赖管理的基础知识

## 🏗️ 理解多模块项目

### 典型项目结构

```
enterprise-app/
├── pom.xml                    # 父POM
├── common/                    # 共享工具
│   ├── pom.xml
│   └── src/main/java/
├── core/                      # 业务逻辑
│   ├── pom.xml
│   └── src/main/java/
├── service/                   # 服务层
│   ├── pom.xml
│   └── src/main/java/
├── api/                       # REST API
│   ├── pom.xml
│   └── src/main/java/
└── web/                       # Web应用程序 (WAR)
    ├── pom.xml
    ├── src/main/java/
    └── src/main/webapp/
```

### 依赖流向

```
web → api → service → core → common
```

## 🚀 设置您的多模块项目

### 步骤1：父POM配置

```xml
<!-- enterprise-app/pom.xml -->
<project>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>enterprise-app</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    
    <modules>
        <module>common</module>
        <module>core</module>
        <module>service</module>
        <module>api</module>
        <module>web</module>
    </modules>
    
    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    
    <dependencyManagement>
        <dependencies>
            <!-- 为所有模块定义版本 -->
            <dependency>
                <groupId>com.example</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- ... 其他模块依赖 -->
        </dependencies>
    </dependencyManagement>
</project>
```

### 步骤2：Web模块配置

```xml
<!-- web/pom.xml -->
<project>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>enterprise-app</artifactId>
        <version>1.0.0</version>
    </parent>
    
    <artifactId>web</artifactId>
    <packaging>war</packaging>
    
    <dependencies>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.example</groupId>
            <artifactId>service</artifactId>
        </dependency>
        <!-- 其他依赖 -->
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.3</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

## 📦 部署多模块项目

### 步骤1：在VSCode中打开项目

1. **打开包含父POM的父目录**
2. **等待Java扩展** 识别项目结构
3. **在Java项目视图中验证模块识别**

### 步骤2：部署Web模块

1. **右键点击web模块文件夹**
2. **选择"Deploy to Tomcat"**
3. **选择您的Tomcat实例**
4. **配置部署设置：**
   - **要部署的模块**: 选择web模块
   - **上下文路径**: `/` 或 `/your-app`
   - **构建策略**: 自动 (针对多模块优化)

### 步骤3：自动依赖解析

扩展会自动：
- ✅ **从POM文件检测模块依赖**
- ✅ **按正确顺序构建模块** (依赖优先)
- ✅ **跳过未更改的模块** 以加快构建速度
- ✅ **正确处理传递依赖**

## ⚡ 构建优化功能

### 智能构建策略

```
对于10+模块的项目：
├── 依赖分析 ✅
├── 增量构建 ✅
├── 并行编译 ✅
└── 智能缓存 ✅
```

### 构建过程流程

```
1. 分析模块依赖关系
2. 检查自上次构建以来的更改
3. 仅构建更改的模块 + 依赖项
4. 在可能的情况下使用并行编译
5. 打包包含所有依赖项的web模块
```

### 性能对比

| 项目规模 | 传统构建 | 优化构建 | 节省时间 |
|----------|----------|----------|----------|
| 5个模块  | 45秒     | 25秒     | 44%      |
| 10个模块 | 2.5分钟  | 1分钟    | 60%      |
| 20+模块  | 8分钟    | 2.5分钟  | 69%      |

## 🐛 调试多模块项目

### 跨模块调试设置

1. **在多个模块中设置断点**：

```java
// 在 web/src/main/java/com/example/web/
@RestController
public class OrderController {
    
    @PostMapping("/orders")
    public ResponseEntity<Order> createOrder(@RequestBody OrderDto dto) {
        // 👈 断点1：Web层
        return orderService.createOrder(dto);
    }
}

// 在 service/src/main/java/com/example/service/
@Service
public class OrderService {
    
    public ResponseEntity<Order> createOrder(OrderDto dto) {
        // 👈 断点2：服务层
        Order order = orderProcessor.process(dto);
        return ResponseEntity.ok(order);
    }
}

// 在 core/src/main/java/com/example/core/
@Component
public class OrderProcessor {
    
    public Order process(OrderDto dto) {
        // 👈 断点3：核心逻辑
        return new Order(dto.getCustomerId(), dto.getItems());
    }
}
```

### 调试配置

扩展会自动创建支持以下功能的launch.json：
- **所有模块的源路径映射**
- **包含所有依赖项的类路径**
- **跨模块边界的符号解析**

## 🔧 高级配置

### 自定义构建命令

对于复杂的构建需求：

```json
// 在 .vscode/settings.json 中
{
    "tomcatManager.buildCommands": {
        "maven": {
            "clean": "mvn clean",
            "compile": "mvn compile -T 4", // 并行编译
            "package": "mvn package -DskipTests -T 4",
            "install": "mvn install -DskipTests -T 4"
        }
    }
}
```

### 模块特定设置

```json
{
    "tomcatManager.multiModule": {
        "buildOrder": ["common", "core", "service", "api", "web"],
        "parallelBuild": true,
        "incrementalBuild": true,
        "skipTests": true
    }
}
```

## 🚀 多模块热部署

### 自动变更检测

扩展监控：
- **所有模块中的Java文件**
- **资源文件** (properties, XML等)
- **Web资源** (JSP, HTML, CSS, JS)
- **POM文件** 的依赖变更

### 智能重建策略

```
common模块变更 → 重建：common, core, service, api, web
core模块变更   → 重建：core, service, api, web
service模块变更 → 重建：service, api, web
web模块变更    → 仅重建：web
```

### 热部署流程

1. **检测到任何模块的文件变更**
2. **根据依赖关系确定受影响的模块**
3. **按正确顺序仅重建必要的模块**
4. **使用更新的依赖项重新部署web应用程序**
5. **尽可能保持应用程序状态**

## 🎯 最佳实践

### 1. 项目结构

```
✅ 好：清晰的关注点分离
enterprise-app/
├── domain/          # 领域模型
├── infrastructure/  # 数据访问，外部服务
├── application/     # 用例，应用服务
├── interfaces/      # 控制器，DTO
└── web/            # Web配置，静态资源

❌ 避免：循环依赖
service → core → service (循环！)
```

### 2. 依赖管理

```xml
<!-- ✅ 好：在父POM中使用dependencyManagement -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-bom</artifactId>
            <version>5.3.21</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>

<!-- ❌ 避免：模块间版本冲突 -->
```

### 3. 构建性能

```xml
<!-- ✅ 好：优化编译器插件 -->
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-compiler-plugin</artifactId>
    <configuration>
        <fork>true</fork>
        <meminitial>128m</meminitial>
        <maxmem>512m</maxmem>
    </configuration>
</plugin>
```

## 🔍 故障排除

### 常见问题

#### 1. 找不到模块
**问题**: "构建期间找不到模块X"
**解决方案**: 
- 检查模块是否在父POM中列出
- 验证模块目录结构
- 从父目录运行 `mvn clean install`

#### 2. 循环依赖
**问题**: 构建因循环依赖错误而失败
**解决方案**:
- 分析依赖图：`mvn dependency:tree`
- 重构以移除循环引用
- 考虑提取公共接口

#### 3. 构建性能慢
**问题**: 大型项目构建时间过长
**解决方案**:
- 启用并行构建：`-T 4`
- 使用增量编译
- 开发期间跳过测试：`-DskipTests`

### 调试问题

#### 找不到源代码
**问题**: 调试器显示编译的类而不是源码
**解决方案**:
1. 确保所有模块都使用调试信息编译
2. 检查launch.json中的源路径
3. 重建所有模块：`mvn clean compile`

## 🚀 下一步

- **[热部署教程](hot-deployment-tutorial-zh.md)** - 优化您的开发工作流
- **[调试教程](debug-tutorial-zh.md)** - 掌握调试技巧
- **[技术文档](../MAVEN_MULTI_MODULE_SUPPORT.md)** - 实现细节

## 🆘 需要帮助？

- **[构建优化指南](../LARGE_PROJECT_BUILD_OPTIMIZATION.md)** - 性能调优
- **[GitHub Issues](https://github.com/your-repo/issues)** - 报告多模块问题

---

**高效构建！ 🏗️✨**
