# 🚀 Hot Deployment Tutorial

Master the hot deployment features of Tomcat Manager for efficient development workflows.

## 🎯 What You'll Learn

- Understanding different types of hot deployment
- Configuring automatic file watching
- Optimizing deployment for different file types
- Troubleshooting hot deployment issues
- Best practices for development workflows

## 📋 Prerequisites

- Tomcat Manager extension installed
- A deployed Java web project
- Basic understanding of web application structure
- Familiarity with <PERSON><PERSON>/Gradle build processes

## 🔥 Understanding Hot Deployment

### What is Hot Deployment?

Hot deployment allows you to update your running application without restarting the server, providing:
- **Faster development cycles**
- **Preserved application state**
- **Immediate feedback on changes**
- **Improved productivity**

### Types of Hot Deployment

#### 1. Static Resource Updates (Instant)
- **CSS files** - Styling changes
- **JavaScript files** - Client-side logic
- **HTML/JSP files** - Template changes
- **Images and assets** - Static content

#### 2. Configuration Updates (Fast)
- **Properties files** - Application configuration
- **XML configuration** - Spring, Hibernate configs
- **Resource bundles** - Internationalization

#### 3. Java Code Updates (Rebuild Required)
- **Controller classes** - Request handling
- **Service classes** - Business logic
- **Entity classes** - Data models
- **Utility classes** - Helper functions

## 🚀 Setting Up Hot Deployment

### Step 1: Enable Hot Deployment

1. **Right-click your Tomcat instance** in Tomcat Explorer
2. **Select "Configure"**
3. **Go to "Hot Deploy" tab**
4. **Enable the following options:**
   - ✅ **Enable Hot Deployment**
   - ✅ **Watch File Changes**
   - ✅ **Auto Rebuild on Java Changes**
   - ✅ **Preserve Session State**

### Step 2: Configure File Watching

```json
// In .vscode/settings.json
{
    "tomcatManager.hotDeploy": {
        "enabled": true,
        "watchPatterns": [
            "src/main/webapp/**/*",
            "src/main/resources/**/*",
            "src/main/java/**/*.java"
        ],
        "excludePatterns": [
            "**/target/**",
            "**/node_modules/**",
            "**/.git/**"
        ],
        "debounceDelay": 1000
    }
}
```

### Step 3: Start with Hot Deploy

1. **Deploy your project** to Tomcat
2. **Start the instance** (▶️ Start button)
3. **Verify hot deploy is active** - look for "🔥 Hot deployment enabled" in logs

## 📁 File Type Specific Behavior

### Static Resources (Instant Update)

```
src/main/webapp/
├── css/
│   └── styles.css      ← Changes deploy instantly
├── js/
│   └── app.js          ← Changes deploy instantly
├── images/
│   └── logo.png        ← Changes deploy instantly
└── WEB-INF/
    └── views/
        └── index.jsp   ← Changes deploy instantly
```

**Example Workflow:**
1. **Edit CSS file** - change background color
2. **Save file** (Ctrl+S)
3. **Refresh browser** - see changes immediately

### Configuration Files (Fast Update)

```
src/main/resources/
├── application.properties  ← Triggers context reload
├── logback.xml             ← Triggers logging reconfiguration
└── messages.properties     ← Triggers resource bundle reload
```

**Example Workflow:**
1. **Edit application.properties** - change database URL
2. **Save file** - triggers automatic reload
3. **Application restarts** with new configuration

### Java Source Files (Rebuild + Deploy)

```
src/main/java/
├── controllers/
│   └── UserController.java    ← Triggers rebuild + redeploy
├── services/
│   └── UserService.java       ← Triggers rebuild + redeploy
└── models/
    └── User.java              ← Triggers rebuild + redeploy
```

**Example Workflow:**
1. **Edit Java file** - add new method
2. **Save file** - triggers automatic build
3. **Wait for build completion** - see progress in output
4. **Application redeploys** with new code

## 🔧 Advanced Configuration

### Custom Build Commands

```json
{
    "tomcatManager.hotDeploy": {
        "buildCommands": {
            "java": "mvn compile",
            "resources": "mvn process-resources",
            "full": "mvn package -DskipTests"
        }
    }
}
```

### Selective File Watching

```json
{
    "tomcatManager.hotDeploy": {
        "fileTypes": {
            "static": {
                "patterns": ["**/*.css", "**/*.js", "**/*.html"],
                "action": "copy",
                "delay": 0
            },
            "java": {
                "patterns": ["**/*.java"],
                "action": "rebuild",
                "delay": 2000
            },
            "config": {
                "patterns": ["**/*.properties", "**/*.xml"],
                "action": "reload",
                "delay": 1000
            }
        }
    }
}
```

### Performance Optimization

```json
{
    "tomcatManager.hotDeploy": {
        "performance": {
            "batchChanges": true,
            "batchDelay": 3000,
            "parallelBuild": true,
            "incrementalBuild": true,
            "skipTests": true
        }
    }
}
```

## 🌐 Real-World Scenarios

### Scenario 1: Frontend Development

**Task**: Updating CSS and JavaScript for UI improvements

```css
/* styles.css - Before */
.header {
    background-color: #333;
    color: white;
}

/* styles.css - After */
.header {
    background-color: #007bff;  /* Changed to blue */
    color: white;
    border-radius: 5px;         /* Added border radius */
}
```

**Workflow:**
1. **Edit CSS file** in VSCode
2. **Save file** (Ctrl+S)
3. **Switch to browser** and refresh (F5)
4. **See changes immediately** - no build required

### Scenario 2: Backend API Development

**Task**: Adding new REST endpoint

```java
@RestController
public class ProductController {
    
    // Existing endpoint
    @GetMapping("/products")
    public List<Product> getAllProducts() {
        return productService.findAll();
    }
    
    // New endpoint - added during development
    @PostMapping("/products")
    public ResponseEntity<Product> createProduct(@RequestBody Product product) {
        Product saved = productService.save(product);
        return ResponseEntity.ok(saved);
    }
}
```

**Workflow:**
1. **Add new method** to controller
2. **Save file** - triggers automatic build
3. **Wait for "Build completed"** message
4. **Test new endpoint** with Postman/browser

### Scenario 3: Configuration Changes

**Task**: Updating database connection settings

```properties
# application.properties - Before
spring.datasource.url=**********************************
spring.datasource.username=testuser

# application.properties - After
spring.datasource.url=**********************************
spring.datasource.username=produser
spring.datasource.password=newpassword
```

**Workflow:**
1. **Edit properties file**
2. **Save file** - triggers context reload
3. **Application restarts** with new database connection
4. **Verify connection** in application logs

## 🔍 Monitoring Hot Deployment

### Output Panel Messages

```
🔥 Hot deployment enabled for instance: my-app
📁 Watching files in: /project/src/main/webapp
📁 Watching files in: /project/src/main/java
📁 Watching files in: /project/src/main/resources

📝 File changed: src/main/webapp/css/styles.css
✅ Static resource updated: styles.css

📝 File changed: src/main/java/com/example/UserController.java
🔨 Building project...
✅ Build completed in 3.2s
🚀 Application redeployed successfully
```

### Status Indicators

- **🔥 Hot Deploy Active** - File watching enabled
- **📁 Watching** - Monitoring file changes
- **🔨 Building** - Compilation in progress
- **🚀 Deploying** - Application update in progress
- **✅ Ready** - Application ready for requests

## 🐛 Troubleshooting

### Issue 1: Changes Not Detected

**Symptoms:**
- File changes don't trigger deployment
- No messages in output panel

**Solutions:**
1. **Check file watching is enabled**:
   ```json
   {
       "tomcatManager.hotDeploy.enabled": true
   }
   ```

2. **Verify file patterns**:
   ```json
   {
       "tomcatManager.hotDeploy.watchPatterns": [
           "src/main/webapp/**/*",
           "src/main/java/**/*.java"
       ]
   }
   ```

3. **Check file is not excluded**:
   ```json
   {
       "tomcatManager.hotDeploy.excludePatterns": [
           "**/target/**"
       ]
   }
   ```

### Issue 2: Build Failures

**Symptoms:**
- Hot deployment triggers but fails
- Compilation errors in output

**Solutions:**
1. **Check Java syntax** - fix compilation errors
2. **Verify dependencies** - ensure all required JARs are available
3. **Clean and rebuild**:
   ```bash
   mvn clean compile
   ```

### Issue 3: Slow Performance

**Symptoms:**
- Hot deployment takes too long
- Multiple rebuilds for single change

**Solutions:**
1. **Enable batching**:
   ```json
   {
       "tomcatManager.hotDeploy.batchChanges": true,
       "tomcatManager.hotDeploy.batchDelay": 3000
   }
   ```

2. **Use incremental builds**:
   ```json
   {
       "tomcatManager.hotDeploy.incrementalBuild": true
   }
   ```

3. **Skip tests during development**:
   ```json
   {
       "tomcatManager.hotDeploy.skipTests": true
   }
   ```

## 🎯 Best Practices

### 1. File Organization

```
✅ Good: Separate static and dynamic content
src/main/
├── webapp/
│   ├── static/     # CSS, JS, images (instant updates)
│   └── WEB-INF/    # JSP, configs (fast updates)
└── java/           # Java code (rebuild required)

❌ Avoid: Mixed file types in same directory
```

### 2. Development Workflow

```
✅ Good: Incremental development
1. Start Tomcat with hot deploy
2. Make small, focused changes
3. Test immediately after each change
4. Commit working changes frequently

❌ Avoid: Large batch changes
```

### 3. Performance Optimization

```
✅ Good: Optimize for development
- Enable incremental builds
- Skip tests during development
- Use file watching with appropriate delays
- Batch multiple changes

❌ Avoid: Full rebuilds for minor changes
```

## 🚀 Next Steps

- **[Debug Tutorial](debug-tutorial.md)** - Combine hot deployment with debugging
- **[Multi-Module Tutorial](multi-module-tutorial.md)** - Hot deployment for complex projects
- **[Technical Documentation](../HOT_DEPLOY_IMPLEMENTATION.md)** - Implementation details

## 🆘 Need Help?

- **[Hot Deploy Error Handling](../HOT_DEPLOY_ERROR_HANDLING_FIX.md)** - Common issues and fixes
- **[GitHub Issues](https://github.com/your-repo/issues)** - Report hot deployment problems

---

**Deploy fast, develop faster! 🚀🔥**
