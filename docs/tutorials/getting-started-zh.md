# 🚀 Tomcat Manager 快速开始指南

欢迎使用VSCode的Tomcat Manager扩展！本指南将帮助您快速上手。

## 📋 前置要求

开始之前，请确保您已安装：

- **VSCode** (版本 1.60.0 或更高)
- **Java开发工具包** (JDK 8 或更高版本)
- **Apache Tomcat** (版本 8.5 或更高)
- **Maven** 或 **Gradle** (用于项目构建)
- **Java Extension Pack** for VSCode (调试功能必需)

## 🔧 安装步骤

### 步骤1：安装扩展

1. 打开VSCode
2. 进入扩展面板 (`Ctrl+Shift+X`)
3. 搜索 "Tomcat Manager"
4. 点击 **安装**

### 步骤2：安装Java扩展包

1. 在扩展面板搜索 "Extension Pack for Java"
2. 点击 **安装** (调试功能必需)

## 🏗️ 创建您的第一个Tomcat实例

### 步骤1：打开命令面板

按 `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)

### 步骤2：创建实例

1. 输入 "Tomcat Manager: Create Instance"
2. 选择该命令
3. 按照设置向导操作：
   - **实例名称**: 输入描述性名称 (例如："开发服务器")
   - **Tomcat路径**: 浏览到您的Tomcat安装目录
   - **JRE路径**: 选择您的Java安装路径 (自动检测)
   - **端口配置**: 使用默认端口或根据需要自定义

### 步骤3：验证创建

- 检查侧边栏的 **Tomcat Explorer** 面板
- 您的新实例应该显示为"已停止"状态

## 📦 部署您的第一个项目

### 步骤1：准备项目

确保您的项目具有正确的结构：

```
your-project/
├── pom.xml (Maven) 或 build.gradle (Gradle)
├── src/
│   └── main/
│       ├── java/
│       └── webapp/
└── target/ (构建后创建)
```

### 步骤2：部署项目

1. 在VSCode资源管理器中 **右键点击** 您的项目文件夹
2. 选择 **"Deploy to Tomcat"**
3. 选择您的Tomcat实例
4. 配置部署设置：
   - **上下文路径**: `/` 表示根部署，`/myapp` 表示自定义路径
   - **构建命令**: 根据项目类型自动检测
   - **输出目录**: Maven通常是 `target`，Gradle是 `build/libs`

### 步骤3：启动实例

1. 在 **Tomcat Explorer** 中找到您的实例
2. 点击 **▶️ 启动** 按钮
3. 等待启动完成
4. 查看输出中的 "✅ Tomcat启动成功！"

## 🌐 访问您的应用程序

### 自动浏览器启动

如果启用了自动启动，您的默认浏览器将自动打开。

### 手动访问

打开浏览器并导航到：
- **根部署**: `http://localhost:8080/`
- **自定义路径**: `http://localhost:8080/your-context-path/`

## 🐛 您的第一次调试会话

### 步骤1：设置断点

1. 打开您的Java源文件
2. 点击行号左侧的空白区域
3. 红色圆点 (●) 表示活动断点

### 步骤2：启动调试模式

1. 在 **Tomcat Explorer** 中，点击 **🐛 调试** 按钮
2. 等待调试启动消息
3. 注意调试端口 (通常是5005)

### 步骤3：连接调试器

1. 按 **F5** 开始调试
2. 如果提示，选择自动生成的调试配置
3. 查看VSCode顶部的调试工具栏

### 步骤4：触发断点

1. 在浏览器中访问您的Web应用程序
2. 导航到执行断点代码的页面
3. VSCode应该在断点处暂停，允许您：
   - 检查变量
   - 单步执行代码
   - 计算表达式

## 🚀 热部署

### 自动文件监控

应用程序运行后：

1. **编辑静态文件** (CSS, JS, HTML) - 更改立即部署
2. **编辑Java文件** - 触发自动重新构建和重新部署
3. **编辑配置文件** - 触发完全重新部署

### 手动重新部署

- 右键点击实例 → **"重新部署"**
- 或使用命令面板: "Tomcat Manager: Redeploy"

## 🔧 常用配置

### 端口冲突

如果遇到端口冲突：

1. 右键点击实例 → **"配置"**
2. 转到 **"端口"** 选项卡
3. 修改冲突的端口
4. 保存并重启实例

### 内存设置

为了更好的性能：

1. 右键点击实例 → **"配置"**
2. 转到 **"JVM"** 选项卡
3. 调整内存设置：
   - **最小堆内存**: 256m (最小值)
   - **最大堆内存**: 512m或更高 (大型应用程序)

### 浏览器设置

自定义浏览器行为：

1. 右键点击实例 → **"配置"**
2. 转到 **"浏览器"** 选项卡
3. 配置：
   - **自动启动**: 启用/禁用自动浏览器打开
   - **默认浏览器**: 选择您偏好的浏览器
   - **默认页面**: 设置自定义着陆页

## 🎯 下一步

现在您已经掌握了基础知识：

1. **[调试教程](debug-tutorial-zh.md)** - 学习高级调试技巧
2. **[多模块项目教程](multi-module-tutorial-zh.md)** - 处理复杂项目
3. **[热部署指南](hot-deployment-tutorial-zh.md)** - 掌握热部署功能

## 🆘 需要帮助？

- **[故障排除指南](../troubleshooting.md)** - 常见问题和解决方案
- **[配置参考](../configuration.md)** - 详细配置选项
- **[GitHub Issues](https://github.com/your-repo/issues)** - 报告错误或请求功能

---

**祝您编码愉快！ 🎉**
