# 🐛 Debug Tutorial for Tom<PERSON> Manager

Master the debugging capabilities of Tomcat Manager with this comprehensive tutorial.

## 🎯 What You'll Learn

- Setting up debug mode with one click
- Understanding automatic debug configuration
- Advanced breakpoint techniques
- Debugging multi-module projects
- Troubleshooting common debug issues

## 📋 Prerequisites

- Tomcat Manager extension installed
- Java Extension Pack for VSCode
- A Java web project (<PERSON><PERSON> or Gradle)
- Basic understanding of Java debugging concepts

## 🚀 Quick Debug Setup

### Step 1: One-Click Debug Start

1. **Open Tomcat Explorer** in VSCode sidebar
2. **Find your instance** in the list
3. **Click the 🐛 Debug button** (next to the ▶️ Start button)
4. **Wait for startup** - you'll see debug information in the output

### Step 2: Automatic Configuration

The extension automatically:
- ✅ **Detects Java version** and configures JDWP parameters
- ✅ **Assigns debug port** (usually 5005, auto-incremented if busy)
- ✅ **Creates launch.json** with correct debug configuration
- ✅ **Upgrades legacy instances** to support debugging

### Step 3: Connect Debugger

1. **Press F5** to start debugging
2. **Select the auto-generated configuration** if prompted
3. **Look for debug toolbar** at the top of VSCode
4. **Verify connection** - breakpoints should become solid red dots

## 🎯 Setting Effective Breakpoints

### Basic Breakpoints

```java
@RestController
public class UserController {
    
    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        // 👈 Click here to set a basic breakpoint
        User user = userService.findById(id);
        
        if (user == null) {
            // 👈 Another good breakpoint location
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(user);
    }
}
```

### Conditional Breakpoints

Right-click on a breakpoint → **Edit Breakpoint** → **Expression**

```java
public void processUsers(List<User> users) {
    for (User user : users) {
        // Condition: user.getId() == 123
        processUser(user); // 👈 Only breaks when user ID is 123
    }
}
```

### Log Breakpoints

Right-click on a breakpoint → **Edit Breakpoint** → **Log Message**

```java
public String calculateResult(int value) {
    // Log Message: "Processing value: {value}"
    return "Result: " + (value * 2); // 👈 Logs without stopping
}
```

## 🏗️ Debugging Multi-Module Projects

### Project Structure Example

```
parent-project/
├── pom.xml
├── core/
│   ├── pom.xml
│   └── src/main/java/com/example/core/
├── service/
│   ├── pom.xml
│   └── src/main/java/com/example/service/
└── web/
    ├── pom.xml
    └── src/main/java/com/example/web/
```

### Cross-Module Debugging

```java
// In web module - Controller
@RestController
public class ApiController {
    
    @Autowired
    private BusinessService businessService; // From service module
    
    @PostMapping("/api/process")
    public ResponseEntity<String> process(@RequestBody String data) {
        // 👈 Set breakpoint here
        String result = businessService.processData(data);
        return ResponseEntity.ok(result);
    }
}

// In service module - Service
@Service
public class BusinessService {
    
    @Autowired
    private CoreProcessor coreProcessor; // From core module
    
    public String processData(String data) {
        // 👈 Set breakpoint here too
        return coreProcessor.process(data);
    }
}

// In core module - Core logic
@Component
public class CoreProcessor {
    
    public String process(String input) {
        // 👈 And here for complete flow debugging
        return "Processed: " + input.toUpperCase();
    }
}
```

### Debug Flow

1. **Set breakpoints** in all modules you want to debug
2. **Start debug mode** from Tomcat Explorer
3. **Connect debugger** (F5)
4. **Make API call** that triggers the flow
5. **Step through** each module in sequence

## 🔧 Advanced Debug Techniques

### Variable Inspection

When stopped at a breakpoint:

```java
public void complexMethod(User user, List<Order> orders) {
    // When stopped here, you can inspect:
    // - user object and all its properties
    // - orders list and individual order objects
    // - Local variables in scope
    // - Method parameters
}
```

**Debug panels to use:**
- **Variables**: See all local variables and their values
- **Watch**: Add custom expressions to monitor
- **Call Stack**: See the method call hierarchy

### Expression Evaluation

In the **Debug Console**, you can evaluate expressions:

```java
// While debugging, type in Debug Console:
user.getName()
orders.size()
orders.stream().filter(o -> o.getStatus() == OrderStatus.PENDING).count()
```

### Step Controls

- **Continue (F5)**: Run to next breakpoint
- **Step Over (F10)**: Execute current line, don't enter methods
- **Step Into (F11)**: Enter method calls
- **Step Out (Shift+F11)**: Exit current method
- **Restart**: Restart debug session
- **Stop**: End debug session

## 🌐 Web Application Debug Scenarios

### Debugging REST APIs

```java
@RestController
public class ProductController {
    
    @PostMapping("/products")
    public ResponseEntity<Product> createProduct(@RequestBody ProductDto dto) {
        // 👈 Breakpoint: Inspect incoming DTO
        
        Product product = productService.create(dto);
        // 👈 Breakpoint: Check created product
        
        return ResponseEntity.ok(product);
    }
}
```

**Test with:**
- Browser developer tools
- Postman
- curl commands
- VSCode REST Client extension

### Debugging Form Submissions

```java
@Controller
public class UserFormController {
    
    @PostMapping("/users/create")
    public String createUser(@ModelAttribute UserForm form, Model model) {
        // 👈 Breakpoint: Inspect form data
        
        if (form.hasErrors()) {
            // 👈 Breakpoint: Debug validation errors
            return "user-form";
        }
        
        userService.save(form.toUser());
        return "redirect:/users";
    }
}
```

### Debugging JSP/Thymeleaf

```java
@Controller
public class PageController {
    
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        List<Widget> widgets = widgetService.getActiveWidgets();
        // 👈 Breakpoint: Check widget data before rendering
        
        model.addAttribute("widgets", widgets);
        return "dashboard"; // JSP/Thymeleaf template
    }
}
```

## 🔍 Troubleshooting Debug Issues

### Issue 1: Debugger Won't Connect

**Symptoms:**
- "Connection refused" error
- Breakpoints remain hollow circles

**Solutions:**
1. **Check debug port** in Tomcat startup logs
2. **Verify Tomcat started in debug mode** (🐛 button, not ▶️)
3. **Check firewall** isn't blocking port 5005
4. **Restart VSCode** and try again

### Issue 2: Debugging Class Files Instead of Source

**Symptoms:**
- Debugger opens .class files instead of .java files
- Variable names are obfuscated

**Solutions:**
1. **Ensure debug compilation**:
   ```xml
   <!-- Maven -->
   <plugin>
       <groupId>org.apache.maven.plugins</groupId>
       <artifactId>maven-compiler-plugin</artifactId>
       <configuration>
           <debug>true</debug>
           <debuglevel>lines,vars,source</debuglevel>
       </configuration>
   </plugin>
   ```

2. **Rebuild project**: `mvn clean compile`
3. **Redeploy** to Tomcat

### Issue 3: Breakpoints Not Hit

**Possible causes:**
- Code not executed by request
- Breakpoint on non-executable line
- Source/compiled code mismatch

**Solutions:**
1. **Verify request path** matches controller mapping
2. **Move breakpoint** to executable code line
3. **Clean and rebuild** project

## 🎯 Best Practices

### 1. Strategic Breakpoint Placement

```java
@Service
public class OrderService {
    
    public Order processOrder(OrderRequest request) {
        // ✅ Good: Method entry point
        log.info("Processing order: {}", request.getId());
        
        Order order = validateOrder(request);
        // ✅ Good: After validation
        
        if (order.requiresApproval()) {
            // ✅ Good: Conditional logic
            return sendForApproval(order);
        }
        
        return completeOrder(order);
        // ✅ Good: Before return
    }
}
```

### 2. Use Conditional Breakpoints Wisely

```java
// Instead of breaking on every iteration:
for (int i = 0; i < 1000; i++) {
    processItem(i); // ❌ Don't break here unconditionally
}

// Use condition: i == 999 or i % 100 == 0
```

### 3. Leverage Log Breakpoints

```java
public void batchProcess(List<Item> items) {
    // Log Message: "Processing batch of {items.size()} items"
    for (Item item : items) {
        // Log Message: "Processing item {item.getId()}"
        process(item);
    }
}
```

## 🚀 Next Steps

- **[Multi-Module Tutorial](multi-module-tutorial.md)** - Advanced project structures
- **[Hot Deployment Guide](hot-deployment-tutorial.md)** - Efficient development workflow
- **[Technical Documentation](../TOMCAT_DEBUG_SUPPORT.md)** - Implementation details

## 🆘 Need Help?

- **[Debug Issues Fix](../DEBUG_JDWP_TRANSPORT_FIX.md)** - Common debug problems
- **[GitHub Issues](https://github.com/your-repo/issues)** - Report debug-related bugs

---

**Happy debugging! 🐛✨**
