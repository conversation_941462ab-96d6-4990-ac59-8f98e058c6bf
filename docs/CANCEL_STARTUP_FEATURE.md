# 🛑 启动取消功能

解决用户在Tomcat启动过程中无法取消操作的问题，提供更好的用户控制体验。

## 🎯 问题描述

### 用户反馈
> "点击tomcat Debug或者Start过程中，我发现代码有问题，想要停止正在启动过程中的Tomcat，却发现没有停止按钮"

### 原有问题
- **无法取消**: 启动过程中没有取消选项
- **强制等待**: 用户必须等待启动完成才能停止
- **资源浪费**: 错误的启动会消耗系统资源
- **开发效率**: 影响快速迭代和调试

## ✅ 解决方案

### 新的用户体验
```
启动过程中：
┌─────────────────────────────────────┐
│ 🚀 MyApp [STARTING...]             │
│                            🛑 [取消] │
└─────────────────────────────────────┘

点击取消后：
┌─────────────────────────────────────┐
│ ⏹️  MyApp [STOPPED]                 │
│                     ▶️ [启动] 🐛 [调试] │
└─────────────────────────────────────┘
```

## 🔧 技术实现

### 1. **取消控制机制**

#### 取消标志管理
```typescript
export class TomcatInstanceManager {
  // 启动取消控制
  private startupCancelTokens: Map<string, boolean> = new Map();
  private startupTimeouts: Map<string, NodeJS.Timeout> = new Map();
}
```

#### 取消启动方法
```typescript
/**
 * 取消正在启动的Tomcat实例
 */
async cancelStartup(instanceId: string): Promise<void> {
  console.log(`🛑 Cancelling startup for instance ${instanceId}`);
  
  const instance = this.instances.get(instanceId);
  if (!instance) {
    throw new Error(`Instance ${instanceId} not found`);
  }

  // 检查是否正在启动
  if (instance.getStatus() !== TomcatInstanceStatus.STARTING) {
    console.warn(`Instance ${instanceId} is not in starting state`);
    return;
  }

  // 设置取消标志
  this.startupCancelTokens.set(instanceId, true);

  // 清理启动超时
  const timeout = this.startupTimeouts.get(instanceId);
  if (timeout) {
    clearTimeout(timeout);
    this.startupTimeouts.delete(instanceId);
  }

  // 终止进程（如果存在）
  const process = this.processes.get(instanceId);
  if (process && !process.killed) {
    console.log(`🔪 Killing startup process for instance ${instanceId}`);
    process.kill('SIGTERM');
    
    // 如果SIGTERM不起作用，使用SIGKILL
    setTimeout(() => {
      if (process && !process.killed) {
        console.log(`🔪 Force killing process for instance ${instanceId}`);
        process.kill('SIGKILL');
      }
    }, 3000);
  }

  // 更新状态为已停止
  this.updateInstanceStatus(instanceId, TomcatInstanceStatus.STOPPED);

  // 清理资源
  this.processes.delete(instanceId);
  this.startupCancelTokens.delete(instanceId);

  // 显示取消消息
  vscode.window.showInformationMessage(
    `🛑 Startup cancelled for instance: ${instance.getName()}`
  );
}
```

### 2. **启动过程中的取消检查**

#### 数据流监听中的检查
```typescript
// 监听stdout
childProcess.stdout?.on("data", (data: Buffer) => {
  // 检查是否被取消
  if (this.startupCancelTokens.get(instanceId)) {
    console.log(`[${instanceId}] Startup cancelled, ignoring stdout data`);
    return;
  }
  
  // 继续处理数据...
});

// 监听stderr
childProcess.stderr?.on("data", (data: Buffer) => {
  // 检查是否被取消
  if (this.startupCancelTokens.get(instanceId)) {
    console.log(`[${instanceId}] Startup cancelled, ignoring stderr data`);
    return;
  }
  
  // 继续处理数据...
});
```

#### 进程退出处理
```typescript
childProcess.on("exit", (code: number | null) => {
  this.processes.delete(instanceId);
  
  // 检查是否是用户取消的
  const wasCancelled = this.startupCancelTokens.get(instanceId);
  if (wasCancelled) {
    outputChannel.appendLine(`\n🛑 Tomcat启动已取消`);
    this.updateInstanceStatus(instanceId, TomcatInstanceStatus.STOPPED);
    this.startupCancelTokens.delete(instanceId);
    return;
  }
  
  // 正常退出处理...
});
```

#### 等待启动完成中的检查
```typescript
await this.waitForStartup(instance, () => {
  // 检查是否被取消
  if (this.startupCancelTokens.get(instanceId)) {
    throw new Error("Startup cancelled by user");
  }
  return startupCompleted;
});

// 再次检查是否被取消
if (this.startupCancelTokens.get(instanceId)) {
  throw new Error("Startup cancelled by user");
}
```

### 3. **UI集成**

#### 命令定义
```json
{
  "command": "tomcatManager.cancelStartup",
  "title": "Cancel Startup",
  "icon": "$(stop-circle)"
}
```

#### 菜单配置
```json
{
  "command": "tomcatManager.cancelStartup",
  "when": "view == tomcatExplorer && viewItem == tomcatInstance-starting",
  "group": "inline"
}
```

#### 命令注册
```typescript
context.subscriptions.push(
  vscode.commands.registerCommand(
    "tomcatManager.cancelStartup",
    async (item) => {
      const instanceId = getInstanceIdFromItem(item);
      if (!instanceId) return;

      try {
        const instance = instanceManager.getInstance(instanceId);
        if (!instance) return;

        await instanceManager.cancelStartup(instanceId);
        statusBarManager.showSuccessMessage(
          `Startup cancelled for ${instance.getName()}`
        );
        tomcatExplorer.refresh();
      } catch (error) {
        statusBarManager.showErrorMessage(
          `Failed to cancel startup: ${error}`
        );
      }
    }
  )
);
```

## 🎯 用户体验改进

### 启动状态可视化

#### STARTING状态显示
```
🚀 MyApp [STARTING...]                    🛑
```

#### 取消按钮位置
- **位置**: 实例名称右侧
- **图标**: 🛑 (stop-circle)
- **提示**: "Cancel Startup"
- **可见性**: 仅在STARTING状态显示

### 操作流程

#### 正常启动流程
```
1. 用户点击▶️ Start或🐛 Debug
2. 状态变为STARTING
3. 显示🛑 Cancel按钮
4. 启动完成后状态变为RUNNING
5. 显示⏹️ Stop按钮
```

#### 取消启动流程
```
1. 启动过程中点击🛑 Cancel
2. 立即终止启动进程
3. 状态变为STOPPED
4. 显示成功取消消息
5. 恢复▶️ Start和🐛 Debug按钮
```

## 🔍 错误处理和边界情况

### 1. **进程终止处理**

#### 优雅终止
```typescript
// 首先尝试SIGTERM
process.kill('SIGTERM');

// 3秒后强制终止
setTimeout(() => {
  if (process && !process.killed) {
    console.log(`🔪 Force killing process for instance ${instanceId}`);
    process.kill('SIGKILL');
  }
}, 3000);
```

#### 进程状态检查
```typescript
if (process && !process.killed) {
  // 进程仍在运行，需要终止
} else {
  // 进程已经结束，只需清理状态
}
```

### 2. **状态同步**

#### 取消标志清理
```typescript
// 设置取消标志
this.startupCancelTokens.set(instanceId, true);

// 操作完成后清理
this.startupCancelTokens.delete(instanceId);
```

#### 超时清理
```typescript
// 清理启动超时
const timeout = this.startupTimeouts.get(instanceId);
if (timeout) {
  clearTimeout(timeout);
  this.startupTimeouts.delete(instanceId);
}
```

### 3. **竞态条件处理**

#### 启动完成与取消的竞态
```typescript
// 在关键点检查取消状态
if (this.startupCancelTokens.get(instanceId)) {
  throw new Error("Startup cancelled by user");
}
```

#### 多次取消请求
```typescript
// 检查是否正在启动
if (instance.getStatus() !== TomcatInstanceStatus.STARTING) {
  console.warn(`Instance ${instanceId} is not in starting state`);
  return;
}
```

## 📊 性能影响

### 资源占用
- **内存**: 每个实例增加约100字节（取消标志）
- **CPU**: 忽略不计（只是标志检查）
- **响应时间**: 取消操作通常在1-3秒内完成

### 进程终止效率
- **SIGTERM**: 优雅终止，通常1-2秒
- **SIGKILL**: 强制终止，立即生效
- **资源清理**: 自动清理进程和文件句柄

## 🚀 使用指南

### 启动并取消
1. **开始启动**: 点击▶️ Start或🐛 Debug按钮
2. **观察状态**: 实例状态变为"STARTING..."
3. **取消启动**: 点击🛑 Cancel按钮
4. **确认取消**: 查看成功取消消息

### 最佳实践
- **及时取消**: 发现问题立即取消，避免资源浪费
- **检查日志**: 取消后检查输出日志了解问题
- **修复问题**: 解决代码或配置问题后重新启动

## 🔮 未来增强

### 可能的改进
1. **取消确认**: 添加取消确认对话框
2. **取消原因**: 允许用户输入取消原因
3. **批量取消**: 支持取消多个实例的启动
4. **智能重试**: 取消后提供智能重试选项

### 高级功能
1. **启动超时**: 自动取消超时的启动
2. **资源监控**: 监控启动过程的资源使用
3. **启动分析**: 分析启动失败的原因
4. **预启动检查**: 启动前检查常见问题

## ✅ 功能验证

### 测试场景
1. **正常取消**: 启动过程中点击取消按钮
2. **快速取消**: 启动刚开始就立即取消
3. **延迟取消**: 启动接近完成时取消
4. **重复取消**: 多次点击取消按钮
5. **并发操作**: 同时启动和取消多个实例

### 预期结果
- ✅ **即时响应**: 点击取消后立即响应
- ✅ **进程终止**: 启动进程被正确终止
- ✅ **状态更新**: 实例状态正确更新为STOPPED
- ✅ **资源清理**: 所有相关资源被正确清理
- ✅ **用户反馈**: 显示清晰的取消成功消息

---

**现在用户可以在启动过程中随时取消操作，获得更好的控制体验！** 🛑✨
