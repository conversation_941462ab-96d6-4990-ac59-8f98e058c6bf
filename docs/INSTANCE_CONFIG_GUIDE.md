# ⚙️ 实例配置界面功能指南

## 🎯 问题解决

### 原问题
实例配置界面显示"开发中..."，无法进行实例配置

### 解决方案
已完成实例配置界面的完整实现，包括：
- ✅ 完整的HTML模板和JavaScript逻辑
- ✅ 五个主要配置标签页
- ✅ 实时状态显示和刷新
- ✅ 端口配置和验证
- ✅ JVM参数配置
- ✅ 浏览器设置
- ✅ 高级配置选项

## 🎨 界面功能

### 1. 基本信息标签页
```
⚙️ 实例名称修改
📝 描述信息编辑
📁 Tomcat路径更改
🔄 实例状态显示和刷新
```

### 2. 端口配置标签页
```
🔌 端口配置
   - HTTP端口 (Web访问)
   - HTTPS端口 (安全Web访问)
   - AJP端口 (Apache连接器)
   - JMX端口 (JMX监控)
   - 管理端口 (Tomcat管理)

🎯 智能端口管理
   - 重新分配端口
   - 检查端口可用性
   - 端口冲突验证
```

### 3. JVM配置标签页
```
☕ Java环境配置
   - JRE路径选择
   - 自动检测默认JRE

🧠 内存配置
   - 最小堆内存 (-Xms)
   - 最大堆内存 (-Xmx)
   - 永久代大小 (-XX:PermSize)
   - 元空间大小 (-XX:MetaspaceSize)

⚡ 其他JVM参数
   - 自定义JVM参数
   - 系统属性设置
```

### 4. 浏览器配置标签页
```
🌐 浏览器设置
   - 启动时自动打开浏览器
   - 浏览器类型选择
   - 自定义浏览器路径
   - 默认启动页面
```

### 5. 高级配置标签页
```
⏱️ 超时设置
   - 启动超时时间
   - 关闭超时时间

🔧 调试和监控
   - 启用JMX监控
   - 启用远程调试
   - 调试端口配置

📂 工作目录显示
```

## 🚀 使用方法

### 打开实例配置界面
1. **方法1**：右键点击Tomcat实例 → "Configure Instance"
2. **方法2**：选中实例后使用命令面板 `Ctrl+Shift+P` → "Tomcat Manager: Configure Instance"

### 配置基本信息
1. **修改实例名称**：
   ```
   基本信息 → 实例名称 → 输入新名称
   ```

2. **更改Tomcat路径**：
   ```
   基本信息 → 基础Tomcat路径 → 点击"📁 更改路径"
   ```

3. **查看实例状态**：
   ```
   基本信息 → 当前状态 → 点击"🔄 刷新状态"
   ```

### 配置端口
1. **修改端口**：
   ```
   端口配置 → 修改各类端口号
   ```

2. **重新分配端口**：
   ```
   端口配置 → 点击"🎯 重新分配端口"
   ```

3. **检查端口可用性**：
   ```
   端口配置 → 点击"🔍 检查端口可用性"
   ```

### 配置JVM
1. **设置JRE路径**：
   ```
   JVM配置 → JRE路径 → 点击"🔄 读取默认"或"📁 选择文件夹"
   ```

2. **配置内存**：
   ```
   JVM配置 → 设置最小/最大堆内存
   ```

3. **添加JVM参数**：
   ```
   JVM配置 → 其他JVM参数 → 输入自定义参数
   例如: -Dfile.encoding=UTF-8
        -Djava.awt.headless=true
   ```

### 配置浏览器
1. **设置浏览器类型**：
   ```
   浏览器配置 → 浏览器类型 → 选择浏览器
   ```

2. **自定义浏览器**：
   ```
   浏览器配置 → 浏览器类型 → 选择"自定义" → 点击"📁 选择"
   ```

### 高级配置
1. **设置超时时间**：
   ```
   高级配置 → 启动超时时间 → 设置秒数
   ```

2. **启用调试**：
   ```
   高级配置 → 启用远程调试 ✓ → 设置调试端口
   ```

## 🔧 技术实现

### 模板文件结构
```
templates/
├── instance-config.html    # 实例配置HTML内容
├── instance-config.js      # 实例配置JavaScript逻辑
└── base.html              # 基础HTML模板
```

### 消息处理
```typescript
// WebViewManager.ts - handleInstanceConfigMessage()
- loadInstance              // 加载实例配置
- updateInstance            // 保存实例配置
- validatePorts             // 验证端口配置
- suggestPorts              // 重新分配端口
- checkPortsAvailability    // 检查端口可用性
- refreshInstanceStatus     // 刷新实例状态
- selectTomcatPath          // 选择Tomcat路径
- selectJrePath             // 选择JRE路径
- loadDefaultJrePath        // 加载默认JRE路径
- selectBrowserPath         // 选择浏览器路径
```

### 配置数据结构
```typescript
interface InstanceConfig {
    instanceId: string;
    name: string;
    description: string;
    baseTomcatPath: string;
    ports: {
        httpPort: number;
        httpsPort: number;
        ajpPort: number;
        jmxPort: number;
        shutdownPort: number;
    };
    jvm: {
        jrePath: string;
        minHeapSize: string;
        maxHeapSize: string;
        permGenSize: string;
        metaspaceSize: string;
        additionalArgs: string[];
    };
    browser: {
        type: string;
        autoOpen: boolean;
        defaultPage: string;
        customPath: string;
    };
    advanced: {
        startupTimeout: number;
        shutdownTimeout: number;
        enableJmx: boolean;
        enableDebug: boolean;
        debugPort: number;
        workingDirectory: string;
    };
}
```

## 🎯 界面预览

### 标签页设计
```
┌─────────────────────────────────────────────────────────┐
│ [基本信息] [端口配置] [JVM配置] [浏览器配置] [高级配置]    │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ 当前标签页内容                                           │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 基本信息示例
```
┌─────────────────────────────────────────────────────────┐
│ 实例名称: [MyApp-Dev                    ]               │
│ 描述:     [开发环境Tomcat实例            ]               │
│ Tomcat路径: [/usr/local/tomcat] [📁 更改路径]           │
│ 当前状态: 🟢 运行中 [🔄 刷新状态]                       │
└─────────────────────────────────────────────────────────┘
```

### 端口配置示例
```
┌─────────────────────────────────────────────────────────┐
│ [🎯 重新分配端口] [🔍 检查端口可用性]                    │
│                                                         │
│ ┌─────┬─────┬─────┬─────┬─────┐                       │
│ │HTTP │HTTPS│ AJP │ JMX │管理 │                       │
│ │8080 │8443 │8009 │9999 │8005 │                       │
│ └─────┴─────┴─────┴─────┴─────┘                       │
│                                                         │
│ ✅ 端口配置有效                                          │
└─────────────────────────────────────────────────────────┘
```

### JVM配置示例
```
┌─────────────────────────────────────────────────────────┐
│ JRE路径: [/usr/lib/jvm/java-11] [🔄读取默认] [📁选择]   │
│                                                         │
│ 最小堆内存: [256m]    最大堆内存: [512m]                │
│ 永久代大小: [128m]    元空间大小: [128m]                │
│                                                         │
│ 其他JVM参数:                                            │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ -Dfile.encoding=UTF-8                               │ │
│ │ -Djava.awt.headless=true                            │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

## 🔍 功能验证

### 测试步骤
1. **启动开发环境**：
   ```bash
   cd "/Users/<USER>/workspace/new_github/tomcat and tomee"
   code .
   # 按F5启动扩展开发主机
   ```

2. **测试实例配置界面**：
   - 创建一个Tomcat实例
   - 右键点击实例选择"Configure Instance"
   - 验证配置界面正常打开

3. **测试各个标签页**：
   - 切换到不同标签页
   - 验证所有功能正常工作

4. **测试配置保存**：
   - 修改一些配置
   - 点击"保存配置"按钮
   - 验证配置保存成功

5. **测试文件选择功能**：
   - 测试Tomcat路径选择
   - 测试JRE路径选择
   - 测试浏览器路径选择

## 🎉 解决结果

### 修复前
- ❌ 实例配置界面显示"开发中..."
- ❌ 无法修改实例配置
- ❌ 缺少配置管理功能

### 修复后
- ✅ 完整的五个标签页配置界面
- ✅ 实时状态显示和刷新
- ✅ 智能端口管理和验证
- ✅ 完整的JVM参数配置
- ✅ 浏览器设置和路径选择
- ✅ 高级配置选项
- ✅ 配置保存和加载功能

## 📚 相关文档

- **[设置面板功能指南](SETTINGS_PANEL_GUIDE.md)** - 全局设置面板说明
- **[模板重构指南](TEMPLATE_REFACTORING_GUIDE.md)** - HTML模板系统说明

现在实例配置界面功能完全正常，您可以通过图形化界面轻松配置所有实例参数！⚙️
