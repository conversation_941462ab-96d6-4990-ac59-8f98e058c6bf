# 🐛 Tomcat调试支持

## 🎯 问题描述

用户反馈：**"很好,现在能在tomcat运行单个web项目或者多模块的web项目,现在却不能debug"**

### 需求分析
- 能够成功部署和运行项目
- 需要添加远程调试功能
- 支持在VSCode中进行断点调试
- 支持调试模式的配置和管理

## ✅ 已实施的调试支持方案

### 1. **端口配置扩展**

#### 新增调试端口配置
```typescript
// src/models/TomcatInstance.ts
export interface PortConfiguration {
  httpPort: number;
  httpsPort: number;
  ajpPort: number;
  jmxPort: number;
  shutdownPort: number;
  debugPort: number;  // 新增调试端口
}
```

#### 默认调试端口范围
```typescript
// src/models/PortManager.ts
this.portRanges = {
  httpPort: 8080,
  httpsPort: 8443,
  ajpPort: 8009,
  jmxPort: 9999,
  shutdownPort: 8005,
  debugStart: 5005,  // 调试端口从5005开始
};
```

### 2. **JVM调试配置**

#### 新增调试相关配置
```typescript
// src/models/TomcatInstance.ts
export interface JvmConfiguration {
  jrePath: string;
  minHeapSize: string;
  maxHeapSize: string;
  additionalArgs: string[];
  debugEnabled: boolean;    // 是否启用调试模式
  debugSuspend: boolean;    // 是否等待调试器连接
}
```

#### 默认调试配置
```typescript
// src/services/TomcatInstanceManager.ts
jvm: {
  jrePath: this.getDefaultJrePath(),
  minHeapSize: "256m",
  maxHeapSize: "512m",
  additionalArgs: [],
  debugEnabled: false,      // 默认禁用调试
  debugSuspend: false,      // 默认不等待调试器
}
```

### 3. **调试参数生成**

#### 智能调试参数配置
```typescript
// src/services/TomcatInstanceManager.ts
private buildEnvironment(config: TomcatInstanceConfiguration) {
  const catalinaOpts = [
    `-Xms${config.jvm.minHeapSize}`,
    `-Xmx${config.jvm.maxHeapSize}`,
    ...config.jvm.additionalArgs,
  ];

  // 添加调试参数
  if (config.jvm.debugEnabled) {
    const debugPort = config.ports.debugPort;
    const suspend = config.jvm.debugSuspend ? "y" : "n";
    
    // Java 9+ 使用新的调试参数格式
    catalinaOpts.push(
      `-agentlib:jdwp=transport=dt_socket,server=y,suspend=${suspend},address=*:${debugPort}`
    );
    
    console.log(`🐛 Debug mode enabled on port ${debugPort} (suspend=${suspend})`);
  }

  return {
    JAVA_HOME: this.getJavaHome(config.jvm.jrePath),
    CATALINA_HOME: config.baseTomcatPath,
    CATALINA_BASE: config.instancePath,
    CATALINA_OPTS: catalinaOpts.join(" "),
  };
}
```

#### 调试参数说明
- **`-agentlib:jdwp`**: Java调试线协议代理
- **`transport=dt_socket`**: 使用socket传输
- **`server=y`**: 作为调试服务器运行
- **`suspend=y/n`**: 是否等待调试器连接后再启动
- **`address=*:port`**: 监听所有接口的指定端口

### 4. **启动日志增强**

#### 详细的调试信息显示
```typescript
// 显示所有端口信息
outputChannel.appendLine(`HTTP端口: ${config.ports.httpPort}`);
outputChannel.appendLine(`HTTPS端口: ${config.ports.httpsPort}`);
outputChannel.appendLine(`AJP端口: ${config.ports.ajpPort}`);
outputChannel.appendLine(`JMX端口: ${config.ports.jmxPort}`);
outputChannel.appendLine(`Shutdown端口: ${config.ports.shutdownPort}`);

// 显示调试信息
if (config.jvm.debugEnabled) {
  outputChannel.appendLine(`🐛 调试端口: ${config.ports.debugPort}`);
  outputChannel.appendLine(`🐛 调试模式: ${config.jvm.debugSuspend ? '等待调试器连接' : '不等待调试器'}`);
  outputChannel.appendLine(`🐛 调试地址: localhost:${config.ports.debugPort}`);
} else {
  outputChannel.appendLine(`调试模式: 已禁用`);
}
```

## 🔧 调试模式详解

### 调试模式类型

#### 1. **非阻塞调试模式** (debugSuspend: false)
```
启动行为：
- Tomcat正常启动
- 调试端口开放
- 可以随时连接调试器
- 不影响正常运行

适用场景：
- 生产环境调试
- 性能分析
- 运行时问题排查
```

#### 2. **阻塞调试模式** (debugSuspend: true)
```
启动行为：
- Tomcat启动后暂停
- 等待调试器连接
- 连接后继续执行
- 适合启动问题调试

适用场景：
- 启动过程调试
- 初始化问题排查
- 配置加载调试
```

### 调试端口分配

#### 自动端口分配
```typescript
// 从5005开始自动分配可用端口
const debugPort = await this.findNextAvailablePort(this.portRanges.debugStart);

// 端口冲突检测
const allPorts = [
  { name: "HTTP", port: ports.httpPort },
  { name: "HTTPS", port: ports.httpsPort },
  { name: "AJP", port: ports.ajpPort },
  { name: "JMX", port: ports.jmxPort },
  { name: "Shutdown", port: ports.shutdownPort },
  { name: "Debug", port: ports.debugPort },  // 调试端口也参与冲突检测
];
```

## 🚀 使用指南

### 启用调试模式

#### 方法1：创建实例时配置
```typescript
const config = {
  name: "Debug Instance",
  baseTomcatPath: "/path/to/tomcat",
  jvm: {
    jrePath: "/path/to/java",
    minHeapSize: "256m",
    maxHeapSize: "512m",
    additionalArgs: [],
    debugEnabled: true,      // 启用调试
    debugSuspend: false,     // 不等待调试器
  }
};
```

#### 方法2：修改现有实例配置
```typescript
// 通过WebView界面修改实例配置
// 或通过API更新配置
await instanceManager.updateInstance(instanceId, {
  jvm: {
    ...existingJvm,
    debugEnabled: true,
    debugSuspend: true,  // 等待调试器连接
  }
});
```

### VSCode调试配置

#### launch.json配置示例
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "Attach to Tomcat",
      "request": "attach",
      "hostName": "localhost",
      "port": 5005,
      "projectName": "your-project-name"
    }
  ]
}
```

### 调试流程

#### 1. **启动调试模式的Tomcat**
```
1. 配置实例启用调试模式
2. 启动Tomcat实例
3. 观察启动日志中的调试信息：
   🐛 调试端口: 5005
   🐛 调试模式: 不等待调试器
   🐛 调试地址: localhost:5005
```

#### 2. **连接调试器**
```
1. 在VSCode中打开项目
2. 设置断点
3. 启动调试配置 "Attach to Tomcat"
4. 调试器连接成功后可以进行调试
```

#### 3. **调试操作**
```
- 设置断点
- 单步执行
- 查看变量
- 评估表达式
- 调用堆栈分析
```

## 🔍 故障排查

### 常见问题

#### 1. **调试端口被占用**
```
错误：Address already in use
解决：
- 检查端口是否被其他进程占用
- 使用不同的调试端口
- 停止占用端口的进程
```

#### 2. **调试器连接失败**
```
错误：Connection refused
解决：
- 确认Tomcat已启动且调试模式已启用
- 检查防火墙设置
- 验证调试端口配置
```

#### 3. **调试器连接后Tomcat无响应**
```
原因：debugSuspend=true且调试器未连接
解决：
- 连接调试器后继续执行
- 或修改配置为debugSuspend=false
```

### 调试信息检查

#### 启动日志检查
```
查看VSCode输出面板中的Tomcat启动日志：
=== Tomcat实例 "Debug Instance" 启动日志 ===
🐛 调试端口: 5005
🐛 调试模式: 不等待调试器
🐛 调试地址: localhost:5005
```

#### JVM参数验证
```
检查CATALINA_OPTS环境变量：
-Xms256m -Xmx512m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005
```

## 📊 性能影响

### 调试模式性能
- **CPU开销**: 调试模式会增加约5-10%的CPU开销
- **内存开销**: 额外占用约10-20MB内存
- **网络开销**: 调试器连接时会有网络通信
- **响应时间**: 断点调试时会暂停执行

### 生产环境建议
- **开发环境**: 可以常开调试模式
- **测试环境**: 按需开启调试模式
- **生产环境**: 建议关闭调试模式，仅在问题排查时临时开启

## 🛡️ 安全考虑

### 调试端口安全
- **网络访问**: 调试端口默认监听所有接口(*)
- **访问控制**: 建议在防火墙中限制调试端口访问
- **生产环境**: 避免在生产环境开放调试端口

### 最佳实践
```typescript
// 开发环境配置
debugEnabled: true,
debugSuspend: false,

// 生产环境配置
debugEnabled: false,
debugSuspend: false,
```

## 📚 相关文档

- **[Java调试官方文档](https://docs.oracle.com/javase/8/docs/technotes/guides/jpda/jdwp-spec.html)** - JDWP协议规范
- **[VSCode Java调试](https://code.visualstudio.com/docs/java/java-debugging)** - VSCode Java调试指南
- **[Tomcat实例管理](../src/services/TomcatInstanceManager.ts)** - 实例管理服务

---

**现在Tomcat实例支持完整的调试功能！可以在VSCode中设置断点，进行远程调试，排查代码问题。** 🐛✨
