# 🔗 自动调试器连接功能

解决用户每次启动Debug模式后还需要手动按F5连接调试器的问题。

## 🎯 问题描述

### 用户反馈
> "为啥每次我启动了debug模式的tomcat，还得点击F5呢？你不能直接执行F5吗，还得我按一下"

### 原有流程的问题
```
1. 用户点击🐛 Debug按钮
2. Tomcat以调试模式启动
3. 系统创建launch.json配置
4. 用户需要手动按F5连接调试器  ← 额外步骤
5. 开始调试
```

## ✅ 解决方案

### 新的自动化流程
```
1. 用户点击🐛 Debug按钮
2. Tomcat以调试模式启动
3. 系统创建launch.json配置
4. 自动等待Tomcat启动完成
5. 自动连接VSCode调试器      ← 自动化
6. 自动打开调试面板          ← 自动化
7. 直接开始调试
```

## 🔧 技术实现

### 1. **自动连接调试器方法**

```typescript
/**
 * 自动连接调试器
 */
private async autoConnectDebugger(instanceId: string): Promise<void> {
  const vscode = require("vscode");
  
  // 检查是否启用自动连接
  const autoConnect = vscode.workspace
    .getConfiguration('tomcatManager')
    .get('debug.autoConnect', true);
  
  if (!autoConnect) {
    console.log('Auto-connect debugger is disabled in settings');
    return;
  }
  
  try {
    // 等待Tomcat完全启动
    await this.waitForTomcatStartup(instanceId);

    // 查找匹配的调试配置
    const configName = `Debug Tomcat (${instance.getName()})`;
    
    // 启动调试会话
    const debugStarted = await vscode.debug.startDebugging(
      vscode.workspace.workspaceFolders?.[0],
      configName
    );

    if (debugStarted) {
      // 自动打开调试面板
      if (autoOpenDebugPanel) {
        vscode.commands.executeCommand("workbench.view.debug");
      }
      
      // 显示成功消息
      vscode.window.showInformationMessage(
        `🐛 Debugger auto-connected! You can now set breakpoints and debug your application.`
      );
    }
  } catch (error) {
    // 错误处理和手动连接选项
  }
}
```

### 2. **智能启动等待**

```typescript
/**
 * 等待Tomcat启动完成
 */
private async waitForTomcatStartup(instanceId: string): Promise<void> {
  return new Promise((resolve, reject) => {
    // 从配置中获取超时时间
    const timeoutSeconds = vscode.workspace
      .getConfiguration("tomcatManager")
      .get("debug.startupTimeout", 30);
    
    const timeout = timeoutSeconds * 1000;
    const checkInterval = 1000; // 每秒检查一次
    
    const checkStartup = () => {
      const instance = this.instances.get(instanceId);
      
      // 检查实例状态
      if (instance.getStatus() === 'running') {
        console.log(`✅ Tomcat startup detected for instance ${instanceId}`);
        resolve();
        return;
      }
      
      // 超时处理
      if (elapsed >= timeout) {
        console.warn(`Timeout waiting for Tomcat startup (${instanceId})`);
        resolve(); // 不要因为超时而失败，继续尝试连接
        return;
      }
      
      setTimeout(checkStartup, checkInterval);
    };

    // 开始检查（等待2秒后开始，给Tomcat启动时间）
    setTimeout(checkStartup, 2000);
  });
}
```

### 3. **集成到调试启动流程**

```typescript
async startInstanceInDebugMode(instanceId: string): Promise<void> {
  // 确保实例支持调试模式
  await this.ensureDebugSupport(instanceId);
  
  // 确保调试配置文件存在
  await this.ensureDebugConfiguration(instanceId);
  
  // 启动Tomcat实例
  await this.startInstanceWithMode(instanceId, true);
  
  // 自动连接调试器 ← 新增步骤
  await this.autoConnectDebugger(instanceId);
}
```

## ⚙️ 配置选项

### 新增的VSCode设置

```json
{
  "tomcatManager.debug.autoConnect": {
    "type": "boolean",
    "default": true,
    "description": "Automatically connect debugger when starting Tomcat in debug mode"
  },
  "tomcatManager.debug.autoOpenDebugPanel": {
    "type": "boolean", 
    "default": true,
    "description": "Automatically open debug panel when debugger connects"
  },
  "tomcatManager.debug.startupTimeout": {
    "type": "number",
    "default": 30,
    "description": "Timeout in seconds to wait for Tomcat startup before connecting debugger"
  }
}
```

### 配置说明

#### `tomcatManager.debug.autoConnect`
- **默认值**: `true`
- **作用**: 启用/禁用自动调试器连接
- **使用场景**: 如果用户希望手动控制调试器连接，可以设置为`false`

#### `tomcatManager.debug.autoOpenDebugPanel`
- **默认值**: `true`
- **作用**: 调试器连接后自动打开调试面板
- **使用场景**: 提供更好的用户体验，直接显示调试界面

#### `tomcatManager.debug.startupTimeout`
- **默认值**: `30`秒
- **作用**: 等待Tomcat启动的最大时间
- **使用场景**: 大型项目可能需要更长的启动时间

## 🎯 用户体验改进

### 改进前的用户操作
```
1. 点击🐛 Debug按钮
2. 等待Tomcat启动
3. 看到"Debug configuration created! Press F5 to connect debugger."
4. 手动按F5
5. 选择调试配置（如果有多个）
6. 等待调试器连接
7. 开始调试
```

### 改进后的用户操作
```
1. 点击🐛 Debug按钮
2. 等待自动完成（Tomcat启动 + 调试器连接）
3. 直接开始调试 ✨
```

### 节省的步骤
- ❌ 不再需要手动按F5
- ❌ 不再需要选择调试配置
- ❌ 不再需要等待手动连接
- ✅ 一键完成所有调试准备工作

## 🔍 错误处理和降级

### 自动连接失败时的处理

#### 1. **调试配置问题**
```typescript
if (!debugStarted) {
  console.warn(`Failed to start debug session for ${configName}`);
  
  // 显示手动连接提示
  vscode.window.showWarningMessage(
    `Debug configuration created but auto-connect failed. Press F5 to connect manually.`,
    "Press F5 for me"
  ).then((selection: string) => {
    if (selection === "Press F5 for me") {
      vscode.commands.executeCommand("workbench.action.debug.start");
    }
  });
}
```

#### 2. **异常错误处理**
```typescript
catch (error) {
  console.error(`Error auto-connecting debugger for instance ${instanceId}:`, error);
  
  // 显示错误消息和手动连接选项
  const errorMessage = error instanceof Error ? error.message : String(error);
  vscode.window.showErrorMessage(
    `Auto-connect failed: ${errorMessage}. You can still connect manually.`,
    "Connect Manually"
  ).then((selection: string) => {
    if (selection === "Connect Manually") {
      vscode.commands.executeCommand("workbench.action.debug.start");
    }
  });
}
```

### 降级策略
1. **自动连接失败** → 提供一键手动连接按钮
2. **配置问题** → 显示具体错误信息和解决建议
3. **超时问题** → 继续尝试连接，不阻止用户操作
4. **用户禁用** → 回退到原有的手动流程

## 🚀 使用指南

### 启用自动连接（默认）
1. 点击🐛 Debug按钮
2. 等待自动完成
3. 开始调试

### 禁用自动连接
1. 打开VSCode设置 (`Ctrl+,`)
2. 搜索 "tomcatManager.debug.autoConnect"
3. 取消勾选
4. 回到手动F5流程

### 自定义超时时间
1. 打开VSCode设置
2. 搜索 "tomcatManager.debug.startupTimeout"
3. 设置适合您项目的启动时间（秒）

## 📊 性能影响

### 资源消耗
- **CPU**: 轻微增加（状态检查循环）
- **内存**: 忽略不计
- **网络**: 无影响

### 时间开销
- **额外等待**: 2-5秒（Tomcat启动检测）
- **总体节省**: 5-10秒（省去手动操作时间）
- **净收益**: 正面，提升用户体验

## 🎉 用户反馈预期

### 预期改进
- ✅ **操作更简单**: 一键启动调试
- ✅ **体验更流畅**: 无需手动干预
- ✅ **错误更少**: 减少忘记按F5的情况
- ✅ **效率更高**: 专注于代码调试而非工具操作

### 可能的用户反馈
- 👍 "太棒了！现在真的是一键调试"
- 👍 "不用再记住按F5了"
- 👍 "调试面板自动打开很方便"
- 🤔 "有时候我不想自动连接" → 提供配置选项解决

## 🔮 未来增强

### 可能的改进方向
1. **智能重连**: 调试器断开时自动重连
2. **多实例支持**: 同时调试多个Tomcat实例
3. **断点预设**: 启动时自动设置常用断点
4. **性能监控**: 调试会话的性能统计

---

**现在用户只需要点击🐛 Debug按钮，就能直接开始调试，无需任何额外操作！** 🎉🔗✨
