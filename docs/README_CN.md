# VSCode Tomcat Manager

一个功能强大的VSCode扩展，用于管理Tomcat服务器实例。支持创建、配置、启动、停止Tomcat实例，以及项目部署等功能。

## 功能特性

- 🚀 **实例管理**：创建、删除、启动、停止Tomcat实例
- ⚙️ **灵活配置**：端口、JVM参数、浏览器设置等
- 📦 **项目部署**：支持Maven/Gradle项目的WAR包部署
- 🔄 **热部署**：文件变化时自动重新部署
- 🌐 **浏览器集成**：自动打开浏览器访问应用
- 📊 **状态监控**：实时显示实例运行状态
- 🎯 **图形化界面**：直观的WebView界面操作
- 📺 **实时日志**：像IDEA一样的启动日志显示
- 🔧 **智能诊断**：启动失败时的详细诊断信息

## 安装

1. 打开VSCode
2. 按 `Ctrl+Shift+X` 打开扩展面板
3. 搜索 "Tomcat Manager"
4. 点击安装

## 快速开始

### 1. 创建Tomcat实例

1. 在侧边栏找到Tomcat图标
2. 右键点击 → "创建实例"
3. 填写实例信息：
   - 实例名称和描述
   - Tomcat安装路径（支持文件夹选择）
   - 端口配置（自动分配可用端口）
   - JVM设置（堆内存、自定义参数）
   - 浏览器集成设置

### 2. 启动实例

- 右键实例 → "启动"
- 或点击实例旁的播放按钮
- 查看VSCode输出面板中的实时启动日志

### 3. 部署项目

1. 右键实例 → "部署项目"
2. 系统自动扫描工作区中的Web项目
3. 选择要部署的项目
4. 配置部署参数（上下文路径、热部署等）
5. 点击"开始部署"

## 主要功能

### 实例管理
- 创建多个独立的Tomcat实例
- 每个实例有独立的配置和端口
- 支持同时运行多个实例
- 实时状态监控和日志显示

### 项目部署
- 自动扫描工作区中的Maven/Gradle Web项目
- 智能过滤WAR包装类型的项目
- 支持WAR包自动构建和部署
- 可配置上下文路径（ROOT或自定义）
- 热部署支持，文件变化自动重新部署
- 防止重复部署机制

### 配置管理
- 端口配置（HTTP、HTTPS、AJP、JMX、Shutdown）
- 自动端口分配，避免冲突
- JVM参数设置（堆内存、系统属性、模块参数）
- 预设JVM参数模板（常用参数、Java 9+模块参数）
- 浏览器集成设置
- 日志配置和实时显示

### 调试和诊断
- 启动失败时的智能诊断
- 详细的错误信息和解决建议
- 实时启动日志显示
- JAVA_HOME自动检测和修复
- 端口占用检测

## 系统要求

- VSCode 1.60.0 或更高版本
- Java 8 或更高版本
- Apache Tomcat 8.5 或更高版本
- Maven 或 Gradle（用于项目构建）

## 📚 文档

- [📖 完整文档](docs/README.md) - 所有文档的索引
- [📋 使用指南](docs/USAGE.md) - 详细的使用说明
- [🎯 图形化界面指南](docs/GRAPHICAL_UI_GUIDE.md) - UI操作指南
- [🔧 技术文档](docs/) - 功能实现和问题修复文档

## 更新日志

查看 [更新日志](docs/CHANGELOG.md) 了解版本更新信息。

## 贡献

欢迎提交Issue和Pull Request！请参考[项目结构文档](docs/PROJECT_STRUCTURE.md)了解代码组织。

## 许可证

MIT License
