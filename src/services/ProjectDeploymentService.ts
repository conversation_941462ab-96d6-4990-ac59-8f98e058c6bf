import * as vscode from "vscode";
import * as fs from "fs";
import * as path from "path";
import { spawn } from "child_process";
import {
  ProjectConfiguration,
  ProjectType,
} from "../models/ProjectConfiguration";
import { TomcatInstance, DeployedApplication } from "../models/TomcatInstance";
import { TomcatInstanceManager } from "./TomcatInstanceManager";

/**
 * 部署状态枚举
 */
export enum DeploymentStatus {
  PENDING = "pending",
  BUILDING = "building",
  DEPLOYING = "deploying",
  SUCCESS = "success",
  FAILED = "failed",
}

/**
 * 部署结果接口
 */
export interface DeploymentResult {
  status: DeploymentStatus;
  message: string;
  warPath?: string;
  deployedUrl?: string;
  buildOutput?: string;
  error?: string;
}

/**
 * 项目部署服务
 */
export class ProjectDeploymentService {
  private static instance: ProjectDeploymentService;
  private instanceManager: TomcatInstanceManager;
  private deploymentQueue: Map<string, Promise<DeploymentResult>> = new Map();

  private constructor() {
    this.instanceManager = TomcatInstanceManager.getInstance();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ProjectDeploymentService {
    if (!ProjectDeploymentService.instance) {
      ProjectDeploymentService.instance = new ProjectDeploymentService();
    }
    return ProjectDeploymentService.instance;
  }

  /**
   * 部署项目到Tomcat实例
   */
  async deployProject(
    project: ProjectConfiguration,
    instanceId: string
  ): Promise<DeploymentResult> {
    const deploymentKey = `${project.getId()}-${instanceId}`;

    // 检查是否已有部署任务在进行
    if (this.deploymentQueue.has(deploymentKey)) {
      return await this.deploymentQueue.get(deploymentKey)!;
    }

    const deploymentPromise = this.performDeployment(project, instanceId);
    this.deploymentQueue.set(deploymentKey, deploymentPromise);

    try {
      const result = await deploymentPromise;
      return result;
    } finally {
      this.deploymentQueue.delete(deploymentKey);
    }
  }

  /**
   * 执行部署
   */
  private async performDeployment(
    project: ProjectConfiguration,
    instanceId: string
  ): Promise<DeploymentResult> {
    try {
      const instance = this.instanceManager.getInstance(instanceId);
      if (!instance) {
        return {
          status: DeploymentStatus.FAILED,
          message: `Tomcat instance ${instanceId} not found`,
          error: "Instance not found",
        };
      }

      // 1. 构建项目
      const buildResult = await this.buildProject(project);
      if (buildResult.status === DeploymentStatus.FAILED) {
        return buildResult;
      }

      // 2. 部署WAR文件
      const deployResult = await this.deployWarFile(project, instance);
      if (deployResult.status === DeploymentStatus.FAILED) {
        return deployResult;
      }

      // 3. 更新实例配置
      await this.updateInstanceDeployment(project, instance);

      // 4. 启动热部署监听（如果启用）
      if (project.isHotDeployEnabled()) {
        const { HotDeployService } = await import("./HotDeployService");
        const hotDeployService = HotDeployService.getInstance();

        // 设置项目的Tomcat实例ID
        project.setTomcatInstanceId(instanceId);

        // 启动热部署监听
        hotDeployService.startWatching(project);
        console.log(`Hot deploy started for project: ${project.getName()}`);
      }

      // 5. 返回成功结果
      const config = instance.getConfiguration();
      const url = instance.getHttpUrl(project.getContextPath());

      return {
        status: DeploymentStatus.SUCCESS,
        message: `Project ${project.getName()} deployed successfully`,
        warPath: project.getWarFilePath(),
        deployedUrl: url,
        buildOutput: buildResult.buildOutput,
      };
    } catch (error) {
      return {
        status: DeploymentStatus.FAILED,
        message: `Deployment failed: ${error}`,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 检查是否需要重新构建
   */
  private needsRebuild(projectPath: string, warPath: string): boolean {
    const fs = require("fs");

    if (!fs.existsSync(warPath)) {
      return true; // WAR文件不存在，需要构建
    }

    try {
      const warStat = fs.statSync(warPath);
      const warTime = warStat.mtime.getTime();

      // 检查源码目录的最新修改时间
      const srcPath = path.join(projectPath, "src");
      if (fs.existsSync(srcPath)) {
        const srcStat = fs.statSync(srcPath);
        if (srcStat.mtime.getTime() > warTime) {
          return true; // 源码比WAR文件新，需要重新构建
        }
      }

      // 检查pom.xml的修改时间
      const pomPath = path.join(projectPath, "pom.xml");
      if (fs.existsSync(pomPath)) {
        const pomStat = fs.statSync(pomPath);
        if (pomStat.mtime.getTime() > warTime) {
          return true; // pom.xml比WAR文件新，需要重新构建
        }
      }

      return false; // 不需要重新构建
    } catch (error) {
      console.warn("Error checking rebuild necessity:", error);
      return true; // 出错时默认重新构建
    }
  }

  /**
   * 检测Maven多模块项目
   */
  private detectMavenMultiModule(projectPath: string): {
    isMultiModule: boolean;
    rootPath?: string;
    modules?: string[];
  } {
    const fs = require("fs");
    const path = require("path");

    // 检查当前目录的pom.xml
    const currentPomPath = path.join(projectPath, "pom.xml");
    if (!fs.existsSync(currentPomPath)) {
      return { isMultiModule: false };
    }

    try {
      const pomContent = fs.readFileSync(currentPomPath, "utf8");

      // 检查是否有modules标签
      if (pomContent.includes("<modules>")) {
        // 这是一个父项目
        const moduleMatches = pomContent.match(/<module>(.*?)<\/module>/g);
        const modules = moduleMatches
          ? moduleMatches.map((match: string) =>
              match.replace(/<\/?module>/g, "")
            )
          : [];

        return {
          isMultiModule: true,
          rootPath: projectPath,
          modules,
        };
      }

      // 检查父目录是否是多模块项目的根
      const parentDir = path.dirname(projectPath);
      const parentPomPath = path.join(parentDir, "pom.xml");

      if (fs.existsSync(parentPomPath)) {
        const parentPomContent = fs.readFileSync(parentPomPath, "utf8");
        if (parentPomContent.includes("<modules>")) {
          const moduleMatches = parentPomContent.match(
            /<module>(.*?)<\/module>/g
          );
          const modules = moduleMatches
            ? moduleMatches.map((match: string) =>
                match.replace(/<\/?module>/g, "")
              )
            : [];

          // 检查当前项目是否是其中一个模块
          const currentModuleName = path.basename(projectPath);
          if (modules.includes(currentModuleName)) {
            return {
              isMultiModule: true,
              rootPath: parentDir,
              modules,
            };
          }
        }
      }

      return { isMultiModule: false };
    } catch (error) {
      console.error("Error detecting Maven multi-module:", error);
      return { isMultiModule: false };
    }
  }

  /**
   * 构建项目
   */
  private async buildProject(
    project: ProjectConfiguration
  ): Promise<DeploymentResult> {
    if (!project.isAutoBuildEnabled()) {
      // 检查WAR文件是否存在，如果默认路径不存在，尝试查找其他WAR文件
      const warPath = this.findWarFile(project);
      if (!warPath) {
        const config = project.getConfiguration();
        const expectedPath = project.getWarFilePath();
        return {
          status: DeploymentStatus.FAILED,
          message: "WAR file not found and auto-build is disabled",
          error: `WAR file not found: ${expectedPath}\n\nProject type: ${config.build.type}\nExpected location: ${config.projectPath}/${config.build.outputDirectory}/${config.build.warFileName}\n\nPlease build the project first or enable auto-build.`,
        };
      }

      // 更新项目配置中的WAR文件名为实际找到的文件名
      if (warPath !== project.getWarFilePath()) {
        const actualWarFileName = path.basename(warPath);
        const config = project.getConfiguration();
        config.build.warFileName = actualWarFileName;
        project.updateConfiguration({ build: config.build });
      }

      return {
        status: DeploymentStatus.SUCCESS,
        message: "Using existing WAR file",
      };
    }

    return new Promise((resolve) => {
      const config = project.getConfiguration();

      // 检测Maven多模块项目
      const multiModuleInfo = this.detectMavenMultiModule(config.projectPath);

      let buildCommand: { command: string; args: string[] };
      let buildCwd: string;

      if (
        multiModuleInfo.isMultiModule &&
        config.build.type === ProjectType.MAVEN
      ) {
        console.log(
          `Detected Maven multi-module project. Root: ${multiModuleInfo.rootPath}`
        );
        console.log(`Modules: ${multiModuleInfo.modules?.join(", ")}`);

        // 对于多模块项目，在根目录执行构建
        buildCwd = multiModuleInfo.rootPath!;

        // 构建特定模块的命令，使用优化参数
        const currentModuleName = path.basename(config.projectPath);

        // 检查是否启用快速构建模式（大型项目推荐）
        const moduleCount = multiModuleInfo.modules?.length || 0;
        const isLargeProject = moduleCount > 10;
        const useFastBuild = isLargeProject; // 大型项目自动启用快速构建

        let buildArgs: string[];

        if (useFastBuild) {
          console.log(
            `Large project detected (${moduleCount} modules), using fast build mode`
          );

          // 检查是否需要重新构建
          const expectedWarPath = path.join(
            config.projectPath,
            config.build.outputDirectory,
            config.build.warFileName
          );
          const needsRebuild = this.needsRebuild(
            config.projectPath,
            expectedWarPath
          );

          if (!needsRebuild) {
            console.log(`📦 WAR file is up-to-date, skipping build`);
            // 直接返回成功，跳过构建
            return {
              status: DeploymentStatus.SUCCESS,
              message: "Using existing up-to-date WAR file",
            };
          }

          // 快速构建参数
          buildArgs = [
            "package", // 不执行clean，保留之前的构建结果
            "-pl",
            currentModuleName,
            "-am", // also-make: 构建依赖模块
            "-T",
            "1C", // 并行构建，使用1个线程每CPU核心
            "-o", // 离线模式，不检查远程仓库更新
            "-Dmaven.test.skip=true", // 跳过测试以加快构建
            "-Dmaven.javadoc.skip=true", // 跳过JavaDoc生成
            "-Dcheckstyle.skip=true", // 跳过代码检查
            "-Dpmd.skip=true", // 跳过PMD检查
            "-Dspotbugs.skip=true", // 跳过SpotBugs检查
            "-Dmaven.source.skip=true", // 跳过源码打包
            "-Dmaven.compile.fork=true", // 并行编译
            "-Dmaven.compiler.maxmem=1024m", // 增加编译器内存
          ];
        } else {
          console.log(
            `Standard project (${moduleCount} modules), using normal build mode`
          );
          // 标准构建参数
          buildArgs = [
            "clean",
            "package",
            "-pl",
            currentModuleName,
            "-am", // also-make: 构建依赖模块
            "-T",
            "1C", // 并行构建
          ];
        }

        buildCommand = {
          command: "mvn",
          args: buildArgs,
        };

        console.log(
          `Building multi-module project with command: mvn ${buildArgs.join(
            " "
          )}`
        );

        if (useFastBuild) {
          console.log(`🚀 Fast build optimizations enabled:`);
          console.log(`  - Skipping clean phase (incremental build)`);
          console.log(`  - Parallel compilation (-T 1C)`);
          console.log(`  - Offline mode (-o)`);
          console.log(`  - Skipping tests and quality checks`);
          console.log(`  - Increased compiler memory`);
        }
      } else {
        // 单模块项目，使用原有逻辑
        buildCommand = this.parseBuildCommand(
          config.build.buildCommand,
          config.projectPath
        );
        buildCwd = config.projectPath;
      }

      const buildProcess = spawn(buildCommand.command, buildCommand.args, {
        cwd: buildCwd,
        stdio: ["pipe", "pipe", "pipe"],
      });

      let buildOutput = "";
      let errorOutput = "";

      buildProcess.stdout?.on("data", (data) => {
        buildOutput += data.toString();
      });

      buildProcess.stderr?.on("data", (data) => {
        errorOutput += data.toString();
      });

      buildProcess.on("exit", (code) => {
        if (code === 0) {
          resolve({
            status: DeploymentStatus.SUCCESS,
            message: "Build completed successfully",
            buildOutput,
          });
        } else {
          // 构建详细的错误信息
          const projectType = config.build.type;

          let detailedError = `Build failed with exit code ${code}\n\n`;
          detailedError += `Project Type: ${projectType}\n`;

          if (multiModuleInfo.isMultiModule) {
            detailedError += `Multi-Module Project: Yes\n`;
            detailedError += `Root Path: ${multiModuleInfo.rootPath}\n`;
            detailedError += `Current Module: ${path.basename(
              config.projectPath
            )}\n`;
            detailedError += `All Modules: ${multiModuleInfo.modules?.join(
              ", "
            )}\n`;
            detailedError += `Build Command: mvn clean package -pl ${path.basename(
              config.projectPath
            )} -am\n`;
            detailedError += `Build Directory: ${buildCwd}\n\n`;
          } else {
            detailedError += `Build Command: ${config.build.buildCommand}\n`;
            detailedError += `Project Path: ${config.projectPath}\n\n`;
          }

          if (errorOutput.trim()) {
            detailedError += `Error Output:\n${errorOutput}\n\n`;
          }

          if (buildOutput.trim()) {
            detailedError += `Build Output:\n${buildOutput}`;
          } else {
            detailedError += `No build output captured. This might indicate:\n`;
            detailedError += `- Build command not found\n`;
            detailedError += `- Incorrect project path\n`;
            detailedError += `- Missing build dependencies\n`;
          }

          resolve({
            status: DeploymentStatus.FAILED,
            message: `Build failed with exit code ${code}`,
            error: detailedError,
            buildOutput,
          });
        }
      });

      buildProcess.on("error", (error) => {
        // 构建详细的进程错误信息
        let detailedError = `Build process error: ${error.message}\n\n`;
        detailedError += `Project Type: ${config.build.type}\n`;

        if (multiModuleInfo.isMultiModule) {
          detailedError += `Multi-Module Project: Yes\n`;
          detailedError += `Root Path: ${multiModuleInfo.rootPath}\n`;
          detailedError += `Current Module: ${path.basename(
            config.projectPath
          )}\n`;
          detailedError += `Build Command: mvn clean package -pl ${path.basename(
            config.projectPath
          )} -am\n`;
          detailedError += `Build Directory: ${buildCwd}\n\n`;
        } else {
          detailedError += `Build Command: ${config.build.buildCommand}\n`;
          detailedError += `Project Path: ${config.projectPath}\n\n`;
        }

        // 添加常见问题的诊断建议
        if (
          error.message.includes("ENOENT") ||
          error.message.includes("command not found")
        ) {
          detailedError += `Possible causes:\n`;
          detailedError += `- Build tool not installed (Maven/Gradle/Ant)\n`;
          detailedError += `- Build command not found in PATH\n`;
          detailedError += `- Incorrect build command configuration\n\n`;

          if (
            multiModuleInfo.isMultiModule ||
            config.build.buildCommand.includes("mvn")
          ) {
            detailedError += `For Maven projects:\n`;
            detailedError += `- Install Maven: https://maven.apache.org/install.html\n`;
            detailedError += `- Verify: mvn --version\n`;
            if (multiModuleInfo.isMultiModule) {
              detailedError += `- For multi-module projects, ensure all modules are properly configured\n`;
              detailedError += `- Check parent pom.xml and module dependencies\n`;
            }
          } else if (config.build.buildCommand.includes("gradle")) {
            detailedError += `For Gradle projects:\n`;
            detailedError += `- Use ./gradlew (wrapper) or install Gradle globally\n`;
            detailedError += `- Verify: gradle --version\n`;
          }
        }

        resolve({
          status: DeploymentStatus.FAILED,
          message: `Build process error: ${error.message}`,
          error: detailedError,
        });
      });
    });
  }

  /**
   * 查找WAR文件
   */
  private findWarFile(project: ProjectConfiguration): string | null {
    const config = project.getConfiguration();
    const outputDir = path.join(
      config.projectPath,
      config.build.outputDirectory
    );

    // 首先检查默认路径
    const defaultWarPath = project.getWarFilePath();
    if (fs.existsSync(defaultWarPath)) {
      return defaultWarPath;
    }

    // 如果默认路径不存在，扫描输出目录查找WAR文件
    if (!fs.existsSync(outputDir)) {
      return null;
    }

    try {
      const files = fs.readdirSync(outputDir);
      const warFiles = files.filter((file) => file.endsWith(".war"));

      if (warFiles.length === 0) {
        return null;
      }

      // 如果只有一个WAR文件，直接返回
      if (warFiles.length === 1) {
        return path.join(outputDir, warFiles[0]);
      }

      // 如果有多个WAR文件，优先选择包含项目名称的文件
      const projectName = config.name.toLowerCase();
      const matchingWar = warFiles.find(
        (file) =>
          file.toLowerCase().includes(projectName) ||
          file.toLowerCase().includes(projectName.replace(/[-_]/g, ""))
      );

      if (matchingWar) {
        return path.join(outputDir, matchingWar);
      }

      // 如果没有匹配的，返回第一个WAR文件
      return path.join(outputDir, warFiles[0]);
    } catch (error) {
      console.error("Error scanning for WAR files:", error);
      return null;
    }
  }

  /**
   * 部署WAR文件
   */
  private async deployWarFile(
    project: ProjectConfiguration,
    instance: TomcatInstance
  ): Promise<DeploymentResult> {
    const warPath = this.findWarFile(project);
    if (!warPath) {
      const config = project.getConfiguration();
      const expectedPath = project.getWarFilePath();
      return {
        status: DeploymentStatus.FAILED,
        message: "WAR file not found",
        error: `WAR file not found: ${expectedPath}\n\nProject type: ${config.build.type}\nExpected location: ${config.projectPath}/${config.build.outputDirectory}/${config.build.warFileName}\n\nPlease build the project first or enable auto-build.`,
      };
    }

    // 更新项目配置中的WAR文件名为实际找到的文件名
    if (warPath !== project.getWarFilePath()) {
      const actualWarFileName = path.basename(warPath);
      const config = project.getConfiguration();
      config.build.warFileName = actualWarFileName;
      project.updateConfiguration({ build: config.build });
    }

    const config = instance.getConfiguration();
    const contextPath = project.getContextPath();

    // 确定部署目标目录（exploded WAR）
    let deployDir: string;
    if (contextPath === "ROOT") {
      deployDir = path.join(config.instancePath, "webapps", "ROOT");
    } else {
      const contextName = contextPath.startsWith("/")
        ? contextPath.substring(1)
        : contextPath;
      deployDir = path.join(config.instancePath, "webapps", contextName);
    }

    try {
      console.log(`Deploying exploded WAR to: ${deployDir}`);

      // 如果目标目录已存在，先删除
      if (fs.existsSync(deployDir)) {
        console.log(`Removing existing deployment directory: ${deployDir}`);
        fs.rmSync(deployDir, { recursive: true, force: true });
      }

      // 创建部署目录
      fs.mkdirSync(deployDir, { recursive: true });

      // 解压WAR文件到部署目录
      console.log(`Extracting WAR file: ${warPath}`);
      await this.extractWarFile(warPath, deployDir);

      // 等待应用部署完成并可用（可配置）
      const enableAvailabilityCheck = false; // 暂时禁用，避免部署卡住
      if (enableAvailabilityCheck) {
        console.log(
          `WAR file copied, waiting for application to be available...`
        );
        try {
          await this.waitForApplicationAvailable(instance, project);
        } catch (error) {
          console.warn(
            `Application availability check failed, but deployment continues:`,
            error
          );
          // 不抛出错误，因为WAR文件已经成功部署
        }
      } else {
        console.log(`Application availability check is disabled, skipping...`);
      }

      console.log(`Exploded WAR deployed successfully to: ${deployDir}`);

      return {
        status: DeploymentStatus.SUCCESS,
        message: "Exploded WAR deployed successfully",
      };
    } catch (error) {
      console.error(`Failed to deploy exploded WAR:`, error);
      return {
        status: DeploymentStatus.FAILED,
        message: `Failed to deploy exploded WAR: ${error}`,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 更新实例部署信息
   */
  private async updateInstanceDeployment(
    project: ProjectConfiguration,
    instance: TomcatInstance
  ): Promise<void> {
    const app: DeployedApplication = {
      id: project.getId(),
      name: project.getName(),
      contextPath: project.getContextPath(),
      warPath: project.getWarFilePath(),
      sourcePath: project.getProjectPath(),
      status: "deployed",
      deployedAt: new Date(),
      url: instance.getHttpUrl(project.getContextPath()),
    };

    instance.addDeployedApp(app);
    project.updateLastDeployed();
    project.setTomcatInstanceId(instance.getId());
  }

  /**
   * 取消部署应用
   */
  async undeployApplication(instanceId: string, appId: string): Promise<void> {
    const instance = this.instanceManager.getInstance(instanceId);
    if (!instance) {
      throw new Error(`Instance ${instanceId} not found`);
    }

    const deployedApps = instance.getDeployedApps();
    const app = deployedApps.find((a) => a.id === appId);
    if (!app) {
      throw new Error(`Application ${appId} not found`);
    }

    const config = instance.getConfiguration();

    // 删除WAR文件和解压目录
    const contextPath = app.contextPath;
    let warFileName: string;
    let deployDir: string;

    if (contextPath === "ROOT") {
      warFileName = "ROOT.war";
      deployDir = "ROOT";
    } else {
      const contextName = contextPath.startsWith("/")
        ? contextPath.substring(1)
        : contextPath;
      warFileName = `${contextName}.war`;
      deployDir = contextName;
    }

    const warPath = path.join(config.instancePath, "webapps", warFileName);
    const deployDirPath = path.join(config.instancePath, "webapps", deployDir);

    // 删除文件和目录
    if (fs.existsSync(warPath)) {
      fs.unlinkSync(warPath);
    }
    if (fs.existsSync(deployDirPath)) {
      fs.rmSync(deployDirPath, { recursive: true, force: true });
    }

    // 停止该应用的热部署监听
    if (app.sourcePath) {
      try {
        const { HotDeployService } = await import("./HotDeployService");
        const hotDeployService = HotDeployService.getInstance();

        console.log(
          `Stopping hot deploy for undeployed app: ${app.name} (${app.sourcePath})`
        );

        // 使用新的stopWatchingByPath方法
        const stopped = hotDeployService.stopWatchingByPath(app.sourcePath);
        if (stopped) {
          console.log(
            `Successfully stopped hot deploy watcher for undeployed app: ${app.name}`
          );
        } else {
          console.log(
            `No active watcher found for undeployed app: ${app.name} (${app.sourcePath})`
          );
        }
      } catch (error) {
        console.error(`Error stopping hot deploy for app ${app.name}:`, error);
      }
    }

    // 从实例中移除应用
    instance.removeDeployedApp(appId);
  }

  /**
   * 检测项目类型
   */
  detectProjectType(projectPath: string): ProjectType {
    if (fs.existsSync(path.join(projectPath, "pom.xml"))) {
      return ProjectType.MAVEN;
    }
    if (
      fs.existsSync(path.join(projectPath, "build.gradle")) ||
      fs.existsSync(path.join(projectPath, "build.gradle.kts"))
    ) {
      return ProjectType.GRADLE;
    }
    return ProjectType.PLAIN_JAVA;
  }

  /**
   * 扫描工作区中的Java Web项目（只包含WAR包装类型的项目）
   */
  async scanWorkspaceForProjects(): Promise<
    { path: string; name: string; type: ProjectType }[]
  > {
    const projects: { path: string; name: string; type: ProjectType }[] = [];
    const workspaceFolders = vscode.workspace.workspaceFolders;

    if (!workspaceFolders) {
      return projects;
    }

    for (const folder of workspaceFolders) {
      const folderPath = folder.uri.fsPath;
      await this.scanDirectoryForProjects(folderPath, projects);
    }

    return projects;
  }

  /**
   * 递归扫描目录查找项目
   */
  private async scanDirectoryForProjects(
    dirPath: string,
    projects: { path: string; name: string; type: ProjectType }[],
    maxDepth: number = 3,
    currentDepth: number = 0
  ): Promise<void> {
    if (currentDepth >= maxDepth) {
      return;
    }

    try {
      const entries = fs.readdirSync(dirPath, { withFileTypes: true });

      // 检查当前目录是否是Web项目
      const projectType = this.detectProjectType(dirPath);
      if (await this.isWebProject(dirPath, projectType)) {
        const projectName = path.basename(dirPath);
        projects.push({
          path: dirPath,
          name: projectName,
          type: projectType,
        });
        return; // 找到项目后不再深入扫描
      }

      // 递归扫描子目录
      for (const entry of entries) {
        if (entry.isDirectory() && !this.shouldSkipDirectory(entry.name)) {
          const subDirPath = path.join(dirPath, entry.name);
          await this.scanDirectoryForProjects(
            subDirPath,
            projects,
            maxDepth,
            currentDepth + 1
          );
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  /**
   * 检查是否为Web项目（WAR包装类型）
   */
  private async isWebProject(
    projectPath: string,
    projectType: ProjectType
  ): Promise<boolean> {
    // 对于普通Java项目，检查是否有web.xml
    if (projectType === ProjectType.PLAIN_JAVA) {
      return this.hasWebXml(projectPath);
    }

    // 对于Maven项目，检查pom.xml中的packaging
    if (projectType === ProjectType.MAVEN) {
      return await this.isMavenWarProject(projectPath);
    }

    // 对于Gradle项目，检查build.gradle中的war插件
    if (projectType === ProjectType.GRADLE) {
      return await this.isGradleWarProject(projectPath);
    }

    return false;
  }

  /**
   * 检查是否有web.xml文件
   */
  private hasWebXml(projectPath: string): boolean {
    const webXmlPaths = [
      path.join(projectPath, "src", "main", "webapp", "WEB-INF", "web.xml"),
      path.join(projectPath, "web", "WEB-INF", "web.xml"),
      path.join(projectPath, "WebContent", "WEB-INF", "web.xml"),
    ];

    return webXmlPaths.some((webXmlPath) => fs.existsSync(webXmlPath));
  }

  /**
   * 检查Maven项目是否为WAR包装类型
   */
  private async isMavenWarProject(projectPath: string): Promise<boolean> {
    const pomPath = path.join(projectPath, "pom.xml");
    if (!fs.existsSync(pomPath)) {
      return false;
    }

    try {
      const pomContent = fs.readFileSync(pomPath, "utf-8");

      // 检查packaging标签
      const packagingMatch = pomContent.match(
        /<packaging>\s*(\w+)\s*<\/packaging>/i
      );
      if (packagingMatch) {
        return packagingMatch[1].toLowerCase() === "war";
      }

      // 如果没有packaging标签，检查是否有webapp目录结构或web.xml
      return (
        this.hasWebXml(projectPath) || this.hasWebappDirectory(projectPath)
      );
    } catch (error) {
      console.error("Error reading pom.xml:", error);
      return false;
    }
  }

  /**
   * 检查Gradle项目是否为WAR项目
   */
  private async isGradleWarProject(projectPath: string): Promise<boolean> {
    const buildGradlePaths = [
      path.join(projectPath, "build.gradle"),
      path.join(projectPath, "build.gradle.kts"),
    ];

    for (const buildPath of buildGradlePaths) {
      if (fs.existsSync(buildPath)) {
        try {
          const buildContent = fs.readFileSync(buildPath, "utf-8");

          // 检查是否应用了war插件
          if (
            buildContent.includes("apply plugin: 'war'") ||
            buildContent.includes('apply plugin: "war"') ||
            buildContent.includes("id 'war'") ||
            buildContent.includes('id "war"') ||
            (buildContent.includes("plugins {") && buildContent.includes("war"))
          ) {
            return true;
          }

          // 检查是否有webapp目录结构
          if (this.hasWebappDirectory(projectPath)) {
            return true;
          }
        } catch (error) {
          console.error("Error reading build.gradle:", error);
        }
      }
    }

    return false;
  }

  /**
   * 检查是否有webapp目录结构
   */
  private hasWebappDirectory(projectPath: string): boolean {
    const webappPaths = [
      path.join(projectPath, "src", "main", "webapp"),
      path.join(projectPath, "web"),
      path.join(projectPath, "WebContent"),
    ];

    return webappPaths.some((webappPath) => fs.existsSync(webappPath));
  }

  /**
   * 检查是否应该跳过目录
   */
  private shouldSkipDirectory(dirName: string): boolean {
    const skipDirs = [
      "node_modules",
      ".git",
      ".svn",
      ".hg",
      "target",
      "build",
      "out",
      "dist",
      ".vscode",
      ".idea",
      ".eclipse",
    ];
    return skipDirs.includes(dirName) || dirName.startsWith(".");
  }

  /**
   * 解析构建命令
   */
  private parseBuildCommand(
    command: string,
    projectPath: string
  ): {
    command: string;
    args: string[];
  } {
    const parts = command.trim().split(/\s+/);
    let actualCommand = parts[0];

    // 对于Gradle项目，检查gradlew是否存在
    if (actualCommand === "./gradlew" || actualCommand === "gradlew") {
      const gradlewPath = path.join(projectPath, "gradlew");
      const gradlewBatPath = path.join(projectPath, "gradlew.bat");

      if (process.platform === "win32" && fs.existsSync(gradlewBatPath)) {
        actualCommand = gradlewBatPath;
      } else if (fs.existsSync(gradlewPath)) {
        actualCommand = gradlewPath;
      } else {
        // 如果gradlew不存在，使用全局gradle命令
        actualCommand = "gradle";
      }
    }

    return {
      command: actualCommand,
      args: parts.slice(1),
    };
  }

  /**
   * 等待应用可用
   */
  private async waitForApplicationAvailable(
    instance: TomcatInstance,
    project: ProjectConfiguration
  ): Promise<void> {
    const config = instance.getConfiguration();
    const contextPath = project.getContextPath();

    // 首先检查Tomcat实例是否在运行
    if (instance.getStatus() !== "running") {
      console.warn(
        `Tomcat instance is not running (status: ${instance.getStatus()}), skipping application availability check`
      );
      return;
    }

    console.log(
      `Waiting for application to be available at context path: ${contextPath}`
    );
    console.log(`Tomcat instance status: ${instance.getStatus()}`);
    console.log(`HTTP port: ${config.ports.httpPort}`);

    const maxAttempts = 30; // 减少到30秒
    const delay = 1000; // 每秒检查一次

    for (let i = 0; i < maxAttempts; i++) {
      try {
        console.log(
          `[${i + 1}/${maxAttempts}] Checking application availability...`
        );

        // 检查应用是否可用
        const isAvailable = await this.checkApplicationHealth(
          config.ports.httpPort,
          contextPath
        );

        if (isAvailable) {
          console.log(
            `✅ Application is available after ${
              i + 1
            } seconds at context: ${contextPath}`
          );
          return;
        }

        // 每3秒打印一次进度
        if (i % 3 === 0 && i > 0) {
          console.log(
            `⏳ Still waiting for application... (${i + 1}/${maxAttempts})`
          );
        }

        await new Promise((resolve) => setTimeout(resolve, delay));
      } catch (error) {
        console.log(
          `❌ Application health check error (attempt ${i + 1}):`,
          error
        );

        // 如果连续失败太多次，可能是配置问题，提前退出
        if (i >= 10) {
          console.warn(
            `Too many consecutive failures, skipping remaining checks`
          );
          break;
        }

        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // 超时后给出警告但不失败，因为应用可能只是启动慢
    console.warn(
      `⚠️ Application availability check timed out after ${maxAttempts} seconds. Application may still be starting.`
    );
    console.warn(
      `This is not a deployment failure - the WAR file has been deployed successfully.`
    );
  }

  /**
   * 检查应用健康状态
   */
  private async checkApplicationHealth(
    port: number,
    contextPath: string
  ): Promise<boolean> {
    const http = require("http");

    // 构建请求路径
    let requestPath = "/";
    if (contextPath && contextPath !== "ROOT") {
      requestPath = contextPath.startsWith("/")
        ? contextPath
        : `/${contextPath}`;
      if (!requestPath.endsWith("/")) {
        requestPath += "/";
      }
    }

    const url = `http://localhost:${port}${requestPath}`;
    console.log(`🔍 Checking application health: ${url}`);

    return new Promise<boolean>((resolve) => {
      const req = http.request(
        {
          hostname: "localhost",
          port: port,
          path: requestPath,
          method: "GET",
          timeout: 2000, // 减少超时时间到2秒
        },
        (res: any) => {
          const statusCode = res.statusCode;
          console.log(`📡 HTTP Response: ${statusCode} for ${url}`);

          // 200-299 表示成功
          // 300-399 表示重定向，也算可用
          // 401, 403 表示需要认证，但应用是可用的
          if (statusCode >= 200 && statusCode < 400) {
            console.log(`✅ Application is healthy (${statusCode})`);
            resolve(true);
          } else if (statusCode === 401 || statusCode === 403) {
            // 认证错误也表示应用可用，只是需要登录
            console.log(
              `🔐 Application is available but requires authentication (${statusCode})`
            );
            resolve(true);
          } else {
            console.log(`❌ Application not ready (${statusCode})`);
            resolve(false);
          }
        }
      );

      req.on("error", (error: any) => {
        console.log(`🚫 Connection failed: ${error.message} (${error.code})`);
        resolve(false);
      });

      req.on("timeout", () => {
        console.log(`⏰ Request timeout for ${url}`);
        req.destroy();
        resolve(false);
      });

      req.end();
    });
  }

  /**
   * 解压WAR文件到指定目录
   */
  private async extractWarFile(
    warPath: string,
    targetDir: string
  ): Promise<void> {
    const yauzl = require("yauzl");
    const fs = require("fs");
    const path = require("path");

    return new Promise((resolve, reject) => {
      yauzl.open(warPath, { lazyEntries: true }, (err: any, zipfile: any) => {
        if (err) {
          reject(err);
          return;
        }

        zipfile.readEntry();
        zipfile.on("entry", (entry: any) => {
          const entryPath = path.join(targetDir, entry.fileName);

          if (/\/$/.test(entry.fileName)) {
            // 目录
            fs.mkdirSync(entryPath, { recursive: true });
            zipfile.readEntry();
          } else {
            // 文件
            const entryDir = path.dirname(entryPath);
            fs.mkdirSync(entryDir, { recursive: true });

            zipfile.openReadStream(entry, (err: any, readStream: any) => {
              if (err) {
                reject(err);
                return;
              }

              const writeStream = fs.createWriteStream(entryPath);
              readStream.pipe(writeStream);

              writeStream.on("close", () => {
                zipfile.readEntry();
              });

              writeStream.on("error", reject);
              readStream.on("error", reject);
            });
          }
        });

        zipfile.on("end", () => {
          resolve();
        });

        zipfile.on("error", reject);
      });
    });
  }

  /**
   * 复制文件
   */
  private async copyFile(src: string, dest: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const readStream = fs.createReadStream(src);
      const writeStream = fs.createWriteStream(dest);

      readStream.on("error", reject);
      writeStream.on("error", reject);
      writeStream.on("finish", resolve);

      readStream.pipe(writeStream);
    });
  }
}
