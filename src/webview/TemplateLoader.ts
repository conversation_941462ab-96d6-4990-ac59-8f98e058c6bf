import * as fs from 'fs';
import * as path from 'path';

/**
 * HTML模板加载器
 */
export class TemplateLoader {
    private static templateCache: Map<string, string> = new Map();
    private static extensionPath: string;

    /**
     * 初始化模板加载器
     */
    static initialize(extensionPath: string): void {
        TemplateLoader.extensionPath = extensionPath;
    }

    /**
     * 加载模板文件
     */
    private static loadTemplate(templateName: string): string {
        if (TemplateLoader.templateCache.has(templateName)) {
            return TemplateLoader.templateCache.get(templateName)!;
        }

        const templatePath = path.join(TemplateLoader.extensionPath, 'templates', templateName);
        
        if (!fs.existsSync(templatePath)) {
            throw new Error(`Template file not found: ${templatePath}`);
        }

        const content = fs.readFileSync(templatePath, 'utf-8');
        TemplateLoader.templateCache.set(templateName, content);
        return content;
    }

    /**
     * 渲染模板
     */
    private static renderTemplate(template: string, variables: { [key: string]: any }): string {
        let rendered = template;
        
        // 替换变量
        for (const [key, value] of Object.entries(variables)) {
            const regex = new RegExp(`{{${key}}}`, 'g');
            rendered = rendered.replace(regex, String(value));
        }
        
        return rendered;
    }

    /**
     * 获取实例创建向导HTML
     */
    static getInstanceWizardHtml(): string {
        const baseTemplate = TemplateLoader.loadTemplate('base.html');
        const contentTemplate = TemplateLoader.loadTemplate('instance-wizard.html');
        const scriptsTemplate = TemplateLoader.loadTemplate('instance-wizard.js');

        return TemplateLoader.renderTemplate(baseTemplate, {
            title: '创建Tomcat实例',
            content: contentTemplate,
            scripts: scriptsTemplate
        });
    }

    /**
     * 获取项目部署界面HTML
     */
    static getProjectDeployHtml(instanceData?: any): string {
        const baseTemplate = TemplateLoader.loadTemplate('base.html');
        const contentTemplate = TemplateLoader.loadTemplate('project-deploy.html');
        const scriptsTemplate = TemplateLoader.loadTemplate('project-deploy.js');

        // 渲染内容模板
        const renderedContent = TemplateLoader.renderTemplate(contentTemplate, {
            instanceName: instanceData?.name || 'Unknown',
            instanceId: instanceData?.id || ''
        });

        // 渲染脚本模板
        const renderedScripts = TemplateLoader.renderTemplate(scriptsTemplate, {
            instanceId: instanceData?.id || ''
        });

        return TemplateLoader.renderTemplate(baseTemplate, {
            title: '部署项目',
            content: renderedContent,
            scripts: renderedScripts
        });
    }

    /**
     * 获取实例配置界面HTML
     */
    static getInstanceConfigHtml(instanceData?: any): string {
        // TODO: 创建实例配置模板
        return TemplateLoader.renderTemplate(TemplateLoader.loadTemplate('base.html'), {
            title: '实例配置',
            content: '<div class="header"><h1>实例配置界面</h1><p>开发中...</p></div>',
            scripts: ''
        });
    }

    /**
     * 获取设置面板HTML
     */
    static getSettingsPanelHtml(): string {
        // TODO: 创建设置面板模板
        return TemplateLoader.renderTemplate(TemplateLoader.loadTemplate('base.html'), {
            title: '设置面板',
            content: '<div class="header"><h1>设置面板</h1><p>开发中...</p></div>',
            scripts: ''
        });
    }

    /**
     * 清除模板缓存
     */
    static clearCache(): void {
        TemplateLoader.templateCache.clear();
    }

    /**
     * 重新加载模板（开发时使用）
     */
    static reloadTemplate(templateName: string): void {
        TemplateLoader.templateCache.delete(templateName);
    }
}
