import * as vscode from "vscode";
import * as path from "path";

/**
 * WebView面板类型枚举
 */
export enum WebViewType {
  INSTANCE_CONFIG = "instanceConfig",
  PROJECT_DEPLOY = "projectDeploy",
  INSTANCE_WIZARD = "instanceWizard",
  SETTINGS_PANEL = "settingsPanel",
}

/**
 * WebView消息类型
 */
export interface WebViewMessage {
  command: string;
  data?: any;
}

/**
 * WebView管理器
 */
export class WebViewManager {
  private static instance: WebViewManager;
  private panels: Map<string, vscode.WebviewPanel> = new Map();
  private context: vscode.ExtensionContext;

  private constructor(context: vscode.ExtensionContext) {
    this.context = context;
    // 初始化模板加载器
    const { TemplateLoader } = require("./TemplateLoader");
    TemplateLoader.initialize(context.extensionPath);
  }

  /**
   * 获取单例实例
   */
  static getInstance(context?: vscode.ExtensionContext): WebViewManager {
    if (!WebViewManager.instance && context) {
      WebViewManager.instance = new WebViewManager(context);
    }
    return WebViewManager.instance;
  }

  /**
   * 创建或显示WebView面板
   */
  createOrShowPanel(
    type: WebViewType,
    title: string,
    data?: any
  ): vscode.WebviewPanel {
    const panelKey = `${type}_${data?.id || "default"}`;

    // 如果面板已存在，直接显示
    if (this.panels.has(panelKey)) {
      const panel = this.panels.get(panelKey)!;
      panel.reveal();
      return panel;
    }

    // 创建新面板
    const panel = vscode.window.createWebviewPanel(
      type,
      title,
      vscode.ViewColumn.One,
      {
        enableScripts: true,
        retainContextWhenHidden: true,
        localResourceRoots: [
          vscode.Uri.file(path.join(this.context.extensionPath, "media")),
        ],
      }
    );

    // 设置HTML内容
    panel.webview.html = this.getWebviewContent(type, panel.webview, data);

    // 处理消息
    panel.webview.onDidReceiveMessage(
      (message: WebViewMessage) => this.handleMessage(type, message, panel),
      undefined,
      this.context.subscriptions
    );

    // 如果是实例配置面板，在WebView加载后立即发送实例数据
    if (type === WebViewType.INSTANCE_CONFIG && data?.instanceId) {
      // 延迟一点时间确保WebView已经完全加载
      setTimeout(() => {
        this.sendInstanceDataToPanel(panel, data.instanceId);
      }, 500);
    }

    // 面板关闭时清理
    panel.onDidDispose(
      () => this.panels.delete(panelKey),
      null,
      this.context.subscriptions
    );

    // 如果是项目部署面板，自动扫描项目
    if (type === WebViewType.PROJECT_DEPLOY) {
      // 延迟一点时间确保WebView已经完全加载
      setTimeout(() => {
        this.autoScanAndSendProjects(panel);
      }, 500);
    }

    this.panels.set(panelKey, panel);
    return panel;
  }

  /**
   * 安全地发送消息到WebView
   */
  private safePostMessage(panel: vscode.WebviewPanel, message: any): boolean {
    try {
      if (!panel.webview) {
        console.warn(
          "WebView is disposed, cannot send message:",
          message.command
        );
        return false;
      }

      panel.webview.postMessage(message);
      return true;
    } catch (error) {
      console.warn("Failed to send message to WebView:", error);
      return false;
    }
  }

  /**
   * 发送实例数据到面板
   */
  private async sendInstanceDataToPanel(
    panel: vscode.WebviewPanel,
    instanceId: string
  ): Promise<void> {
    try {
      console.log("Sending instance data for ID:", instanceId);
      const { TomcatInstanceManager } = await import(
        "../services/TomcatInstanceManager"
      );
      const instanceManager = TomcatInstanceManager.getInstance();
      const instance = instanceManager.getInstance(instanceId);

      if (instance) {
        const instanceData = instance.toJSON();
        console.log("Instance data to send:", instanceData);
        const sent = this.safePostMessage(panel, {
          command: "instanceLoaded",
          data: { instance: instanceData },
        });
        if (sent) {
          console.log("Instance data sent successfully");
        }
      } else {
        console.error("Instance not found:", instanceId);
      }
    } catch (error) {
      console.error("Failed to send instance data:", error);
    }
  }

  /**
   * 获取WebView HTML内容
   */
  private getWebviewContent(
    type: WebViewType,
    webview: vscode.Webview,
    data?: any
  ): string {
    const mediaPath = vscode.Uri.file(
      path.join(this.context.extensionPath, "media")
    );
    const mediaUri = webview.asWebviewUri(mediaPath);

    switch (type) {
      case WebViewType.INSTANCE_WIZARD:
        return this.getInstanceWizardHtml(webview, mediaUri, data);
      case WebViewType.INSTANCE_CONFIG:
        return this.getInstanceConfigHtml(webview, mediaUri, data);
      case WebViewType.PROJECT_DEPLOY:
        return this.getProjectDeployHtml(webview, mediaUri, data);
      case WebViewType.SETTINGS_PANEL:
        return this.getSettingsPanelHtml(webview, mediaUri, data);
      default:
        return "<html><body><h1>Unknown panel type</h1></body></html>";
    }
  }

  /**
   * 处理WebView消息
   */
  private async handleMessage(
    type: WebViewType,
    message: WebViewMessage,
    panel: vscode.WebviewPanel
  ): Promise<void> {
    switch (type) {
      case WebViewType.INSTANCE_WIZARD:
        await this.handleInstanceWizardMessage(message, panel);
        break;
      case WebViewType.INSTANCE_CONFIG:
        await this.handleInstanceConfigMessage(message, panel);
        break;
      case WebViewType.PROJECT_DEPLOY:
        await this.handleProjectDeployMessage(message, panel);
        break;
      case WebViewType.SETTINGS_PANEL:
        await this.handleSettingsMessage(message, panel);
        break;
    }
  }

  /**
   * 实例向导消息处理
   */
  private async handleInstanceWizardMessage(
    message: WebViewMessage,
    panel: vscode.WebviewPanel
  ): Promise<void> {
    const { TomcatInstanceManager } = await import(
      "../services/TomcatInstanceManager"
    );
    const instanceManager = TomcatInstanceManager.getInstance();

    switch (message.command) {
      case "createInstance":
        try {
          const instance = await instanceManager.createInstance(message.data);
          panel.webview.postMessage({
            command: "instanceCreated",
            data: { success: true, instance: instance.toJSON() },
          });
          vscode.window.showInformationMessage(
            `Tomcat实例 "${instance.getName()}" 创建成功！`
          );
        } catch (error) {
          panel.webview.postMessage({
            command: "instanceCreated",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`创建实例失败: ${error}`);
        }
        break;
      case "validatePorts":
        try {
          const { PortManager } = await import("../models/PortManager");
          const portManager = PortManager.getInstance();
          const errors = await portManager.validatePortConfiguration(
            message.data
          );
          panel.webview.postMessage({
            command: "portsValidated",
            data: { errors },
          });
        } catch (error) {
          console.error("Port validation error:", error);
        }
        break;
      case "suggestPorts":
        try {
          const { PortManager } = await import("../models/PortManager");
          const portManager = PortManager.getInstance();
          const ports = await portManager.generateAvailablePortConfiguration();
          panel.webview.postMessage({
            command: "portsSuggested",
            data: { ports },
          });
        } catch (error) {
          console.error("Port suggestion error:", error);
        }
        break;
      case "selectTomcatPath":
        try {
          console.log("selectTomcatPath message received");
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择Tomcat安装目录",
            title: "选择Tomcat安装目录",
          });

          console.log("Dialog result:", result);
          if (result && result[0]) {
            console.log(
              "Sending tomcatPathSelected message with path:",
              result[0].fsPath
            );
            panel.webview.postMessage({
              command: "tomcatPathSelected",
              data: { path: result[0].fsPath },
            });
          } else {
            console.log("No folder selected or dialog cancelled");
          }
        } catch (error) {
          console.error("Select Tomcat path error:", error);
          vscode.window.showErrorMessage(`选择文件夹失败: ${error}`);
        }
        break;
      case "selectJrePath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择JRE目录",
            title: "选择JRE目录",
          });

          if (result && result[0]) {
            panel.webview.postMessage({
              command: "jrePathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select JRE path error:", error);
        }
        break;
      case "loadDefaultJrePath":
        try {
          const { ConfigurationManager } = await import(
            "../services/ConfigurationManager"
          );
          const configManager = ConfigurationManager.getInstance();
          const globalConfig = configManager.getGlobalConfiguration();

          // 尝试获取默认JRE路径
          let defaultJrePath = globalConfig.defaultJrePath;

          // 如果没有配置，尝试从环境变量获取
          if (!defaultJrePath) {
            defaultJrePath = process.env.JAVA_HOME || "";
          }

          // 如果还是没有，尝试常见的JRE路径
          if (!defaultJrePath) {
            const commonPaths = this.getCommonJrePaths();
            for (const path of commonPaths) {
              try {
                const fs = await import("fs");
                if (fs.existsSync(path)) {
                  defaultJrePath = path;
                  break;
                }
              } catch (error) {
                // 继续尝试下一个路径
              }
            }
          }

          panel.webview.postMessage({
            command: "defaultJrePathLoaded",
            data: { path: defaultJrePath || "" },
          });
        } catch (error) {
          console.error("Load default JRE path error:", error);
          panel.webview.postMessage({
            command: "defaultJrePathLoaded",
            data: { path: "" },
          });
        }
        break;
    }
  }

  /**
   * 实例配置消息处理
   */
  private async handleInstanceConfigMessage(
    message: WebViewMessage,
    panel: vscode.WebviewPanel
  ): Promise<void> {
    const { TomcatInstanceManager } = await import(
      "../services/TomcatInstanceManager"
    );
    const instanceManager = TomcatInstanceManager.getInstance();

    switch (message.command) {
      case "updateInstance":
        try {
          await instanceManager.updateInstance(
            message.data.instanceId,
            message.data.updates
          );
          panel.webview.postMessage({
            command: "instanceUpdated",
            data: { success: true },
          });
          vscode.window.showInformationMessage("实例配置更新成功！");
        } catch (error) {
          panel.webview.postMessage({
            command: "instanceUpdated",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`更新配置失败: ${error}`);
        }
        break;
      case "loadInstance":
        try {
          const instance = instanceManager.getInstance(message.data.instanceId);
          if (instance) {
            panel.webview.postMessage({
              command: "instanceLoaded",
              data: { instance: instance.toJSON() },
            });
          }
        } catch (error) {
          console.error("Load instance error:", error);
        }
        break;
      case "validatePorts":
        try {
          const { PortManager } = await import("../models/PortManager");
          const portManager = PortManager.getInstance();
          const errors = await portManager.validatePortConfiguration(
            message.data.ports,
            message.data.instanceId
          );
          panel.webview.postMessage({
            command: "portsValidated",
            data: { errors },
          });
        } catch (error) {
          console.error("Port validation error:", error);
        }
        break;
      case "suggestPorts":
        try {
          const { PortManager } = await import("../models/PortManager");
          const portManager = PortManager.getInstance();
          const ports = await portManager.generateAvailablePortConfiguration();
          panel.webview.postMessage({
            command: "portsSuggested",
            data: { ports },
          });
        } catch (error) {
          console.error("Port suggestion error:", error);
        }
        break;
      case "checkPortsAvailability":
        try {
          const { PortDetectionService } = await import(
            "../services/PortDetectionService"
          );
          const portService = PortDetectionService.getInstance();
          const results: { [key: string]: boolean } = {};

          for (const [portName, portNumber] of Object.entries(
            message.data.ports
          )) {
            results[portName] = await portService.isPortAvailable(
              portNumber as number
            );
          }

          panel.webview.postMessage({
            command: "portsAvailabilityChecked",
            data: { results },
          });
        } catch (error) {
          console.error("Port availability check error:", error);
        }
        break;
      case "refreshInstanceStatus":
        try {
          const instance = instanceManager.getInstance(message.data.instanceId);
          if (instance) {
            const status = instance.getStatus();
            panel.webview.postMessage({
              command: "instanceStatusRefreshed",
              data: { status },
            });
          }
        } catch (error) {
          console.error("Refresh instance status error:", error);
        }
        break;
      case "selectTomcatPath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择Tomcat安装目录",
            title: "选择Tomcat安装目录",
          });

          if (result && result[0]) {
            panel.webview.postMessage({
              command: "tomcatPathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select Tomcat path error:", error);
        }
        break;
      case "selectJrePath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择JRE目录",
            title: "选择JRE目录",
          });

          if (result && result[0]) {
            panel.webview.postMessage({
              command: "jrePathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select JRE path error:", error);
        }
        break;
      case "loadDefaultJrePath":
        try {
          const { ConfigurationManager } = await import(
            "../services/ConfigurationManager"
          );
          const configManager = ConfigurationManager.getInstance();
          const globalConfig = configManager.getGlobalConfiguration();

          let defaultJrePath = globalConfig.defaultJrePath;

          if (!defaultJrePath) {
            defaultJrePath = process.env.JAVA_HOME || "";
          }

          if (!defaultJrePath) {
            const commonPaths = this.getCommonJrePaths();
            for (const path of commonPaths) {
              try {
                const fs = await import("fs");
                if (fs.existsSync(path)) {
                  defaultJrePath = path;
                  break;
                }
              } catch (error) {
                // 继续尝试下一个路径
              }
            }
          }

          panel.webview.postMessage({
            command: "defaultJrePathLoaded",
            data: { path: defaultJrePath || "" },
          });
        } catch (error) {
          console.error("Load default JRE path error:", error);
          panel.webview.postMessage({
            command: "defaultJrePathLoaded",
            data: { path: "" },
          });
        }
        break;
      case "selectBrowserPath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            openLabel: "选择浏览器",
            title: "选择浏览器可执行文件",
            filters:
              process.platform === "win32"
                ? {
                    Executable: ["exe"],
                  }
                : undefined,
          });

          if (result && result[0]) {
            panel.webview.postMessage({
              command: "browserPathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select browser path error:", error);
        }
        break;
    }
  }

  /**
   * 项目部署消息处理
   */
  private async handleProjectDeployMessage(
    message: WebViewMessage,
    panel: vscode.WebviewPanel
  ): Promise<void> {
    const { ProjectDeploymentService } = await import(
      "../services/ProjectDeploymentService"
    );
    const { ProjectConfiguration } = await import(
      "../models/ProjectConfiguration"
    );
    const deploymentService = ProjectDeploymentService.getInstance();

    switch (message.command) {
      case "scanProjects":
        try {
          const projects = await deploymentService.scanWorkspaceForProjects();
          panel.webview.postMessage({
            command: "projectsScanned",
            data: { projects },
          });
        } catch (error) {
          console.error("Scan projects error:", error);
        }
        break;
      case "deployProject":
        try {
          // 检查是否已有相同项目的部署记录
          const { TomcatInstanceManager } = await import(
            "../services/TomcatInstanceManager"
          );
          const instanceManager = TomcatInstanceManager.getInstance();
          const instance = instanceManager.getInstance(message.data.instanceId);

          let projectConfig: any;
          let existingApp: any = null;

          if (instance) {
            // 查找是否已有相同项目路径的部署应用
            const deployedApps = instance.getDeployedApps();
            existingApp = deployedApps.find(
              (app) => app.sourcePath === message.data.projectPath
            );
          }

          if (existingApp) {
            // 如果找到现有应用，使用相同的ID创建配置
            projectConfig = ProjectConfiguration.createWithId(
              existingApp.id,
              message.data.projectPath,
              message.data.projectName
            );
          } else {
            // 如果没有找到，创建新的配置
            projectConfig = ProjectConfiguration.createDefault(
              message.data.projectPath,
              message.data.projectName
            );
          }

          // 根据项目类型更新构建配置
          if (message.data.projectType) {
            const { ProjectType } = await import(
              "../models/ProjectConfiguration"
            );
            const buildConfig = projectConfig.getConfiguration().build;

            switch (message.data.projectType) {
              case ProjectType.GRADLE:
                buildConfig.type = ProjectType.GRADLE;
                buildConfig.buildCommand = "./gradlew war";
                buildConfig.outputDirectory = "build/libs";
                buildConfig.warFileName = `${message.data.projectName}.war`;
                buildConfig.autoBuild = true; // 确保Gradle项目启用自动构建
                break;
              case ProjectType.PLAIN_JAVA:
                buildConfig.type = ProjectType.PLAIN_JAVA;
                buildConfig.buildCommand = "ant war";
                buildConfig.outputDirectory = "dist";
                buildConfig.warFileName = `${message.data.projectName}.war`;
                buildConfig.autoBuild = true; // 也启用自动构建
                break;
              case ProjectType.MAVEN:
                // Maven项目确保启用自动构建
                buildConfig.type = ProjectType.MAVEN;
                buildConfig.buildCommand = "mvn clean package";
                buildConfig.outputDirectory = "target";
                buildConfig.warFileName = `${message.data.projectName}.war`;
                buildConfig.autoBuild = true;
                break;
              // 默认情况下启用自动构建
            }

            projectConfig.updateConfiguration({ build: buildConfig });
          }

          projectConfig.setContextPath(message.data.contextPath);

          const result = await deploymentService.deployProject(
            projectConfig,
            message.data.instanceId
          );

          // 使用安全方法发送部署结果
          this.safePostMessage(panel, {
            command: "projectDeployed",
            data: { success: result.status === "success", result },
          });

          if (result.status === "success") {
            vscode.window.showInformationMessage(
              `项目 "${message.data.projectName}" 部署成功！`
            );

            // 刷新Tomcat Explorer视图以显示新部署的应用
            vscode.commands.executeCommand("tomcatManager.refresh");
          } else {
            vscode.window.showErrorMessage(`部署失败: ${result.message}`);
          }
        } catch (error) {
          // 使用安全方法发送错误消息
          this.safePostMessage(panel, {
            command: "projectDeployed",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`部署失败: ${error}`);
        }
        break;
    }
  }

  /**
   * 设置面板消息处理
   */
  private async handleSettingsMessage(
    message: WebViewMessage,
    panel: vscode.WebviewPanel
  ): Promise<void> {
    const { ConfigurationManager } = await import(
      "../services/ConfigurationManager"
    );
    const configManager = ConfigurationManager.getInstance();

    switch (message.command) {
      case "updateSettings":
        try {
          await configManager.updateGlobalConfiguration(message.data);
          this.safePostMessage(panel, {
            command: "settingsUpdated",
            data: { success: true },
          });
          vscode.window.showInformationMessage("设置更新成功！");
        } catch (error) {
          this.safePostMessage(panel, {
            command: "settingsUpdated",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`更新设置失败: ${error}`);
        }
        break;
      case "loadSettings":
        try {
          const settings = configManager.getGlobalConfiguration();
          this.safePostMessage(panel, {
            command: "settingsLoaded",
            data: { settings },
          });
        } catch (error) {
          console.error("Load settings error:", error);
        }
        break;
      case "selectBaseTomcatPath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择Tomcat目录",
            title: "选择Tomcat安装目录",
          });

          if (result && result[0]) {
            this.safePostMessage(panel, {
              command: "baseTomcatPathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select base Tomcat path error:", error);
        }
        break;
      case "selectDefaultJrePath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择JRE目录",
            title: "选择JRE/JDK目录",
          });

          if (result && result[0]) {
            this.safePostMessage(panel, {
              command: "defaultJrePathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select default JRE path error:", error);
        }
        break;
      case "selectCustomBrowserPath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            openLabel: "选择浏览器",
            title: "选择浏览器可执行文件",
            filters:
              process.platform === "win32"
                ? {
                    Executable: ["exe"],
                  }
                : undefined,
          });

          if (result && result[0]) {
            this.safePostMessage(panel, {
              command: "customBrowserPathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select custom browser path error:", error);
        }
        break;
      case "exportSettings":
        try {
          const result = await vscode.window.showSaveDialog({
            defaultUri: vscode.Uri.file("tomcat-manager-settings.json"),
            filters: {
              JSON: ["json"],
            },
            saveLabel: "导出设置",
          });

          if (result) {
            await configManager.exportConfiguration(result.fsPath);
            this.safePostMessage(panel, {
              command: "settingsExported",
              data: { success: true },
            });
            vscode.window.showInformationMessage("设置导出成功！");
          }
        } catch (error) {
          this.safePostMessage(panel, {
            command: "settingsExported",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`导出设置失败: ${error}`);
        }
        break;
      case "importSettings":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            openLabel: "导入设置",
            title: "选择设置文件",
            filters: {
              JSON: ["json"],
            },
          });

          if (result && result[0]) {
            await configManager.importConfiguration(result[0].fsPath);
            this.safePostMessage(panel, {
              command: "settingsImported",
              data: { success: true },
            });
            vscode.window.showInformationMessage("设置导入成功！");
          }
        } catch (error) {
          this.safePostMessage(panel, {
            command: "settingsImported",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`导入设置失败: ${error}`);
        }
        break;
      case "resetToDefaults":
        try {
          await configManager.resetConfiguration();
          this.safePostMessage(panel, {
            command: "defaultsReset",
            data: { success: true },
          });
          vscode.window.showInformationMessage("已恢复默认设置！");
        } catch (error) {
          this.safePostMessage(panel, {
            command: "defaultsReset",
            data: { success: false, error: String(error) },
          });
          vscode.window.showErrorMessage(`恢复默认设置失败: ${error}`);
        }
        break;
      case "selectDefaultTomcatPath":
        try {
          const result = await vscode.window.showOpenDialog({
            canSelectFiles: false,
            canSelectFolders: true,
            canSelectMany: false,
            openLabel: "选择默认Tomcat目录",
            title: "选择默认Tomcat目录",
          });

          if (result && result[0]) {
            this.safePostMessage(panel, {
              command: "defaultTomcatPathSelected",
              data: { path: result[0].fsPath },
            });
          }
        } catch (error) {
          console.error("Select default Tomcat path error:", error);
        }
        break;
      case "detectDefaultJrePath":
        try {
          // 使用与实例创建相同的JRE检测逻辑
          const globalConfig = configManager.getGlobalConfiguration();
          let defaultJrePath = globalConfig.defaultJrePath;

          if (!defaultJrePath) {
            defaultJrePath = process.env.JAVA_HOME || "";
          }

          if (!defaultJrePath) {
            const commonPaths = this.getCommonJrePaths();
            for (const path of commonPaths) {
              try {
                const fs = await import("fs");
                if (fs.existsSync(path)) {
                  defaultJrePath = path;
                  break;
                }
              } catch (error) {
                // 继续尝试下一个路径
              }
            }
          }

          this.safePostMessage(panel, {
            command: "defaultJrePathDetected",
            data: { path: defaultJrePath || "" },
          });
        } catch (error) {
          console.error("Detect default JRE path error:", error);
          this.safePostMessage(panel, {
            command: "defaultJrePathDetected",
            data: { path: "" },
          });
        }
        break;
      case "getWorkspaceRoot":
        try {
          const workspaceFolders = vscode.workspace.workspaceFolders;
          const workspaceRoot =
            workspaceFolders && workspaceFolders.length > 0
              ? workspaceFolders[0].uri.fsPath
              : "";

          this.safePostMessage(panel, {
            command: "workspaceRootLoaded",
            data: { path: workspaceRoot },
          });
        } catch (error) {
          console.error("Get workspace root error:", error);
        }
        break;
    }
  }

  /**
   * 获取实例向导HTML
   */
  private getInstanceWizardHtml(
    webview: vscode.Webview,
    mediaUri: vscode.Uri,
    data?: any
  ): string {
    const { TemplateLoader } = require("./TemplateLoader");
    return TemplateLoader.getInstanceWizardHtml();
  }

  /**
   * 获取实例配置HTML
   */
  private getInstanceConfigHtml(
    webview: vscode.Webview,
    mediaUri: vscode.Uri,
    data?: any
  ): string {
    const { TemplateLoader } = require("./TemplateLoader");
    return TemplateLoader.getInstanceConfigHtml(data);
  }

  /**
   * 获取项目部署HTML
   */
  private getProjectDeployHtml(
    webview: vscode.Webview,
    mediaUri: vscode.Uri,
    data?: any
  ): string {
    const { TemplateLoader } = require("./TemplateLoader");
    return TemplateLoader.getProjectDeployHtml(data);
  }

  /**
   * 自动扫描并发送项目列表
   */
  private async autoScanAndSendProjects(
    panel: vscode.WebviewPanel
  ): Promise<void> {
    try {
      const { ProjectDeploymentService } = await import(
        "../services/ProjectDeploymentService"
      );
      const deploymentService = ProjectDeploymentService.getInstance();
      const projects = await deploymentService.scanWorkspaceForProjects();

      panel.webview.postMessage({
        command: "projectsScanned",
        data: { projects },
      });
    } catch (error) {
      console.error("Auto scan projects error:", error);
      panel.webview.postMessage({
        command: "projectsScanned",
        data: { projects: [] },
      });
    }
  }

  /**
   * 获取设置面板HTML
   */
  private getSettingsPanelHtml(
    webview: vscode.Webview,
    mediaUri: vscode.Uri,
    data?: any
  ): string {
    const { TemplateLoader } = require("./TemplateLoader");
    return TemplateLoader.getSettingsPanelHtml();
  }

  /**
   * 获取常见的JRE路径
   */
  private getCommonJrePaths(): string[] {
    const platform = process.platform;

    if (platform === "win32") {
      return [
        "C:\\Program Files\\Java\\jdk-11",
        "C:\\Program Files\\Java\\jdk-17",
        "C:\\Program Files\\Java\\jdk-21",
        "C:\\Program Files\\Java\\jre-11",
        "C:\\Program Files\\Java\\jre-17",
        "C:\\Program Files (x86)\\Java\\jdk-11",
        "C:\\Program Files (x86)\\Java\\jdk-17",
        "C:\\Program Files (x86)\\Java\\jre-11",
        "C:\\Program Files (x86)\\Java\\jre-17",
      ];
    } else if (platform === "darwin") {
      return [
        "/Library/Java/JavaVirtualMachines/jdk-11.jdk/Contents/Home",
        "/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home",
        "/Library/Java/JavaVirtualMachines/jdk-21.jdk/Contents/Home",
        "/System/Library/Java/JavaVirtualMachines/1.8.0.jdk/Contents/Home",
        "/usr/libexec/java_home",
      ];
    } else {
      return [
        "/usr/lib/jvm/java-11-openjdk",
        "/usr/lib/jvm/java-17-openjdk",
        "/usr/lib/jvm/java-21-openjdk",
        "/usr/lib/jvm/java-8-openjdk",
        "/usr/lib/jvm/default-java",
        "/opt/java/openjdk",
        "/usr/java/latest",
      ];
    }
  }
}
