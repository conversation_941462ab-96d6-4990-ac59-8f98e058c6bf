import * as vscode from "vscode";
import { TomcatExplorer } from "./views/TomcatExplorer";
import { StatusBarManager } from "./views/StatusBarManager";
import { WebViewManager, WebViewType } from "./webview/WebViewManager";
import { TomcatInstanceManager } from "./services/TomcatInstanceManager";
import { ProjectDeploymentService } from "./services/ProjectDeploymentService";
import { HotDeployService } from "./services/HotDeployService";
import { BrowserService } from "./services/BrowserService";
import { PortDetectionService } from "./services/PortDetectionService";
import { ConfigurationManager } from "./services/ConfigurationManager";
import {
  TomcatInstanceConfiguration,
  BrowserType,
} from "./models/TomcatInstance";
import { ProjectConfiguration } from "./models/ProjectConfiguration";

/**
 * 扩展激活函数
 */
export function activate(context: vscode.ExtensionContext) {
  console.log("Tomcat Manager extension is now active!");

  // 初始化服务
  const instanceManager = TomcatInstanceManager.getInstance();
  const deploymentService = ProjectDeploymentService.getInstance();
  const hotDeployService = HotDeployService.getInstance();
  const browserService = BrowserService.getInstance();
  const portDetectionService = PortDetectionService.getInstance();
  const configManager = ConfigurationManager.getInstance();

  // 初始化UI组件
  const tomcatExplorer = new TomcatExplorer(context);
  const statusBarManager = StatusBarManager.getInstance();
  const webViewManager = WebViewManager.getInstance(context);

  // 注册命令
  registerCommands(context, {
    instanceManager,
    deploymentService,
    hotDeployService,
    browserService,
    portDetectionService,
    configManager,
    tomcatExplorer,
    statusBarManager,
    webViewManager,
  });

  // 清理资源
  context.subscriptions.push(tomcatExplorer, statusBarManager, {
    dispose: () => hotDeployService.stopAllWatching(),
  });
}

/**
 * 注册所有命令
 */
function registerCommands(
  context: vscode.ExtensionContext,
  services: {
    instanceManager: TomcatInstanceManager;
    deploymentService: ProjectDeploymentService;
    hotDeployService: HotDeployService;
    browserService: BrowserService;
    portDetectionService: PortDetectionService;
    configManager: ConfigurationManager;
    tomcatExplorer: TomcatExplorer;
    statusBarManager: StatusBarManager;
    webViewManager: WebViewManager;
  }
) {
  const {
    instanceManager,
    deploymentService,
    hotDeployService,
    browserService,
    portDetectionService,
    configManager,
    tomcatExplorer,
    statusBarManager,
    webViewManager,
  } = services;

  // 添加Tomcat实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.addTomcatInstance",
      async () => {
        try {
          webViewManager.createOrShowPanel(
            WebViewType.INSTANCE_WIZARD,
            "创建Tomcat实例"
          );
        } catch (error) {
          statusBarManager.showErrorMessage(`打开创建向导失败: ${error}`);
        }
      }
    )
  );

  // 启动实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.startInstance",
      async (item) => {
        const instanceId = getInstanceIdFromItem(item);
        if (!instanceId) return;

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) return;

          const task = instanceManager.startInstance(instanceId);
          statusBarManager.showInstanceProgress(
            instance.getName(),
            "Starting",
            task
          );
          await task;

          statusBarManager.showSuccessMessage(
            `${instance.getName()} started successfully`
          );
          tomcatExplorer.refresh();
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to start instance: ${error}`
          );
        }
      }
    )
  );

  // 停止实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.stopInstance",
      async (item) => {
        const instanceId = getInstanceIdFromItem(item);
        if (!instanceId) return;

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) return;

          const task = instanceManager.stopInstance(instanceId);
          statusBarManager.showInstanceProgress(
            instance.getName(),
            "Stopping",
            task
          );
          await task;

          statusBarManager.showSuccessMessage(
            `${instance.getName()} stopped successfully`
          );
          tomcatExplorer.refresh();
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to stop instance: ${error}`
          );
        }
      }
    )
  );

  // 重启实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.restartInstance",
      async (item) => {
        const instanceId = getInstanceIdFromItem(item);
        if (!instanceId) return;

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) return;

          const task = instanceManager.restartInstance(instanceId);
          statusBarManager.showInstanceProgress(
            instance.getName(),
            "Restarting",
            task
          );
          await task;

          statusBarManager.showSuccessMessage(
            `${instance.getName()} restarted successfully`
          );
          tomcatExplorer.refresh();
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to restart instance: ${error}`
          );
        }
      }
    )
  );

  // 删除实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.deleteInstance",
      async (item) => {
        const instanceId = getInstanceIdFromItem(item);
        if (!instanceId) return;

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) return;

          const confirmation = await vscode.window.showWarningMessage(
            `Are you sure you want to delete the Tomcat instance "${instance.getName()}"?`,
            { modal: true },
            "Delete"
          );

          if (confirmation === "Delete") {
            await instanceManager.deleteInstance(instanceId);
            statusBarManager.showSuccessMessage(
              `${instance.getName()} deleted successfully`
            );
            tomcatExplorer.refresh();
          }
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to delete instance: ${error}`
          );
        }
      }
    )
  );

  // 部署项目
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.deployProject",
      async (item) => {
        const instanceId = getInstanceIdFromItem(item);
        if (!instanceId) return;

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) return;

          webViewManager.createOrShowPanel(
            WebViewType.PROJECT_DEPLOY,
            `部署项目到 ${instance.getName()}`,
            { id: instanceId, name: instance.getName() }
          );
        } catch (error) {
          statusBarManager.showErrorMessage(`打开部署界面失败: ${error}`);
        }
      }
    )
  );

  // 在浏览器中打开
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.openInBrowser",
      async (instanceIdOrItem, contextPath = "") => {
        let instanceId: string | undefined;

        if (typeof instanceIdOrItem === "string") {
          instanceId = instanceIdOrItem;
        } else {
          instanceId = getInstanceIdFromItem(instanceIdOrItem);
        }

        if (!instanceId) return;

        try {
          await instanceManager.openInBrowser(instanceId, contextPath);
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to open in browser: ${error}`
          );
        }
      }
    )
  );

  // 显示日志
  context.subscriptions.push(
    vscode.commands.registerCommand("tomcatManager.showLogs", async (item) => {
      const instanceId = getInstanceIdFromItem(item);
      if (!instanceId) return;

      try {
        const instance = instanceManager.getInstance(instanceId);
        if (!instance) return;

        const config = instance.getConfiguration();
        const logPath = config.logPath;

        // 打开日志文件
        const logFile = vscode.Uri.file(`${logPath}/catalina.out`);
        await vscode.window.showTextDocument(logFile);
      } catch (error) {
        statusBarManager.showErrorMessage(`Failed to show logs: ${error}`);
      }
    })
  );

  // 配置实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.configureInstance",
      async (item) => {
        console.log("Configure instance command triggered with item:", item);

        let instanceId = getInstanceIdFromItem(item);
        console.log("Extracted instanceId:", instanceId);

        // 如果无法从item获取instanceId，尝试从树视图的选择中获取
        if (!instanceId) {
          console.log("Trying to get instanceId from tree selection...");
          const selectedItem = tomcatExplorer.getSelectedInstance();
          console.log("Selected item from tree:", selectedItem);

          if (selectedItem) {
            instanceId = getInstanceIdFromItem(selectedItem);
            console.log("InstanceId from selected item:", instanceId);
          }
        }

        // 如果还是没有instanceId，显示选择对话框
        if (!instanceId) {
          console.log("Still no instanceId, showing selection dialog...");
          const instances = instanceManager.getAllInstances();

          if (instances.length === 0) {
            vscode.window.showWarningMessage(
              "没有可用的Tomcat实例，请先创建一个实例"
            );
            return;
          }

          if (instances.length === 1) {
            // 只有一个实例，直接使用
            instanceId = instances[0].getId();
            console.log("Using single instance:", instanceId);
          } else {
            // 多个实例，让用户选择
            const items = instances.map((instance) => ({
              label: instance.getName(),
              description: `HTTP: ${
                instance.getConfiguration().ports.httpPort
              }`,
              instanceId: instance.getId(),
            }));

            const selected = await vscode.window.showQuickPick(items, {
              placeHolder: "选择要配置的Tomcat实例",
            });

            if (!selected) {
              return;
            }

            instanceId = selected.instanceId;
            console.log("User selected instance:", instanceId);
          }
        }

        if (!instanceId) {
          console.log("No instanceId found after all attempts");
          vscode.window.showErrorMessage("无法获取实例ID，请重试");
          return;
        }

        try {
          const instance = instanceManager.getInstance(instanceId);
          if (!instance) {
            console.log("Instance not found for ID:", instanceId);
            vscode.window.showErrorMessage("找不到指定的实例");
            return;
          }

          console.log(
            "Opening configuration panel for instance:",
            instance.getName()
          );

          // 使用WebView界面打开实例配置
          webViewManager.createOrShowPanel(
            WebViewType.INSTANCE_CONFIG,
            `配置实例: ${instance.getName()}`,
            {
              id: instanceId,
              name: instance.getName(),
              instance: instance.toJSON(),
            }
          );
        } catch (error) {
          console.error("Error opening configuration:", error);
          statusBarManager.showErrorMessage(
            `Failed to open configuration: ${error}`
          );
        }
      }
    )
  );

  // 启动所有实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.startAllInstances",
      async () => {
        try {
          const instances = instanceManager.getAllInstances();
          const stoppedInstances = instances.filter(
            (instance) => instance.getStatus() === "stopped"
          );

          if (stoppedInstances.length === 0) {
            statusBarManager.showWarningMessage(
              "No stopped instances to start"
            );
            return;
          }

          const tasks = stoppedInstances.map((instance) =>
            instanceManager.startInstance(instance.getId())
          );

          statusBarManager.showProgress(
            "Starting all instances...",
            Promise.all(tasks)
          );
          await Promise.all(tasks);

          statusBarManager.showSuccessMessage(
            `Started ${stoppedInstances.length} instance(s)`
          );
          tomcatExplorer.refresh();
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to start all instances: ${error}`
          );
        }
      }
    )
  );

  // 停止所有实例
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.stopAllInstances",
      async () => {
        try {
          const instances = instanceManager.getAllInstances();
          const runningInstances = instances.filter(
            (instance) => instance.getStatus() === "running"
          );

          if (runningInstances.length === 0) {
            statusBarManager.showWarningMessage("No running instances to stop");
            return;
          }

          const tasks = runningInstances.map((instance) =>
            instanceManager.stopInstance(instance.getId())
          );

          statusBarManager.showProgress(
            "Stopping all instances...",
            Promise.all(tasks)
          );
          await Promise.all(tasks);

          statusBarManager.showSuccessMessage(
            `Stopped ${runningInstances.length} instance(s)`
          );
          tomcatExplorer.refresh();
        } catch (error) {
          statusBarManager.showErrorMessage(
            `Failed to stop all instances: ${error}`
          );
        }
      }
    )
  );

  // 打开设置面板
  context.subscriptions.push(
    vscode.commands.registerCommand("tomcatManager.openSettings", async () => {
      try {
        webViewManager.createOrShowPanel(
          WebViewType.SETTINGS_PANEL,
          "Tomcat Manager 设置"
        );
      } catch (error) {
        statusBarManager.showErrorMessage(`打开设置面板失败: ${error}`);
      }
    })
  );

  // 临时测试命令 - 测试配置界面
  context.subscriptions.push(
    vscode.commands.registerCommand(
      "tomcatManager.testConfigureInstance",
      async () => {
        try {
          console.log("Test configure command triggered");
          const instances = instanceManager.getAllInstances();
          console.log(
            "Available instances:",
            instances.map((i) => ({ id: i.getId(), name: i.getName() }))
          );

          if (instances.length === 0) {
            vscode.window.showWarningMessage(
              "没有可用的Tomcat实例，请先创建一个实例"
            );
            return;
          }

          const instance = instances[0]; // 使用第一个实例进行测试
          console.log("Using instance for test:", instance.getName());

          webViewManager.createOrShowPanel(
            WebViewType.INSTANCE_CONFIG,
            `配置实例: ${instance.getName()}`,
            {
              id: instance.getId(),
              name: instance.getName(),
              instance: instance.toJSON(),
            }
          );

          vscode.window.showInformationMessage("测试配置界面已打开");
        } catch (error) {
          console.error("Test configure error:", error);
          vscode.window.showErrorMessage(`测试配置失败: ${error}`);
        }
      }
    )
  );
}

/**
 * 从树节点项获取实例ID
 */
function getInstanceIdFromItem(item: any): string | undefined {
  console.log("getInstanceIdFromItem called with:", item);
  console.log("Item type:", typeof item);
  console.log("Item constructor:", item?.constructor?.name);
  console.log("Item properties:", item ? Object.keys(item) : "null/undefined");

  // 尝试多种可能的属性名
  if (item) {
    // 直接的instanceId属性
    if (item.instanceId) {
      console.log("Found instanceId:", item.instanceId);
      return item.instanceId;
    }

    // 检查是否是TomcatTreeItem实例
    if (item.constructor?.name === "TomcatTreeItem") {
      console.log("Item is TomcatTreeItem, checking properties...");
      console.log("Item.instanceId:", item.instanceId);
      console.log("Item.itemType:", item.itemType);

      if (item.instanceId) {
        console.log("Found instanceId in TomcatTreeItem:", item.instanceId);
        return item.instanceId;
      }
    }

    // 检查是否有id属性
    if (item.id) {
      console.log("Found id property:", item.id);
      return item.id;
    }

    // 检查是否有resourceUri或其他可能的属性
    if (item.resourceUri) {
      console.log("Found resourceUri:", item.resourceUri);
    }

    // 打印所有属性值以便调试
    for (const key of Object.keys(item)) {
      console.log(`Item.${key}:`, item[key]);
    }
  }

  console.log("No instanceId found in item");
  return undefined;
}

/**
 * 显示添加实例向导
 */
async function showAddInstanceWizard(): Promise<
  Partial<TomcatInstanceConfiguration> | undefined
> {
  // 获取实例名称
  const name = await vscode.window.showInputBox({
    prompt: "Enter Tomcat instance name",
    placeHolder: "e.g., MyApp-Tomcat",
    validateInput: (value) => {
      if (!value || !value.trim()) {
        return "Instance name is required";
      }
      return null;
    },
  });

  if (!name) return undefined;

  // 获取基础Tomcat路径
  const baseTomcatPath = await vscode.window.showInputBox({
    prompt: "Enter base Tomcat installation path",
    placeHolder: "e.g., /usr/local/tomcat or C:\\apache-tomcat-9.0.65",
    validateInput: (value) => {
      if (!value || !value.trim()) {
        return "Base Tomcat path is required";
      }
      return null;
    },
  });

  if (!baseTomcatPath) return undefined;

  // 获取JRE路径
  const jrePath = await vscode.window.showInputBox({
    prompt: "Enter JRE path (optional)",
    placeHolder:
      "e.g., /usr/lib/jvm/java-11-openjdk or leave empty for default",
    value: process.env.JAVA_HOME || "",
  });

  // 获取描述
  const description = await vscode.window.showInputBox({
    prompt: "Enter instance description (optional)",
    placeHolder: "e.g., Development server for MyApp",
  });

  return {
    name: name.trim(),
    description: description?.trim() || "",
    baseTomcatPath: baseTomcatPath.trim(),
    jvm: {
      jrePath: jrePath?.trim() || process.env.JAVA_HOME || "java",
      minHeapSize: "256m",
      maxHeapSize: "512m",
      additionalArgs: [],
    },
    browser: {
      type: BrowserType.DEFAULT,
      autoOpen: true,
      defaultPage: "",
    },
  };
}

/**
 * 显示项目选择对话框
 */
async function showProjectSelectionDialog(): Promise<
  ProjectConfiguration | undefined
> {
  const deploymentService = ProjectDeploymentService.getInstance();

  // 扫描工作区中的项目
  const projects = await deploymentService.scanWorkspaceForProjects();

  if (projects.length === 0) {
    vscode.window.showWarningMessage("No Java web projects found in workspace");
    return undefined;
  }

  // 显示项目选择快速选择
  const selectedProject = await vscode.window.showQuickPick(
    projects.map((project) => ({
      label: project.name,
      description: project.path,
      detail: `Type: ${project.type}`,
      project,
    })),
    {
      placeHolder: "Select a project to deploy",
      canPickMany: false,
    }
  );

  if (!selectedProject) return undefined;

  // 获取上下文路径
  const contextPath = await vscode.window.showInputBox({
    prompt: "Enter context path",
    placeHolder: "e.g., /myapp or ROOT for root context",
    value: "ROOT",
    validateInput: (value) => {
      if (!value || !value.trim()) {
        return "Context path is required";
      }
      if (value !== "ROOT" && !value.startsWith("/")) {
        return 'Context path must start with "/" or be "ROOT"';
      }
      return null;
    },
  });

  if (contextPath === undefined) return undefined;

  // 创建项目配置
  const projectConfig = ProjectConfiguration.createDefault(
    selectedProject.project.path,
    selectedProject.project.name
  );

  projectConfig.setContextPath(contextPath.trim());

  return projectConfig;
}

/**
 * 显示实例配置对话框
 */
async function showInstanceConfigurationDialog(
  currentConfig: TomcatInstanceConfiguration
): Promise<Partial<TomcatInstanceConfiguration> | undefined> {
  const options = [
    "Update Ports",
    "Update JVM Settings",
    "Update Browser Settings",
    "Update Instance Info",
  ];

  const selected = await vscode.window.showQuickPick(options, {
    placeHolder: "What would you like to configure?",
  });

  if (!selected) return undefined;

  switch (selected) {
    case "Update Ports":
      return await showPortConfigurationDialog(currentConfig.ports);
    case "Update JVM Settings":
      return await showJvmConfigurationDialog(currentConfig.jvm);
    case "Update Browser Settings":
      return await showBrowserConfigurationDialog(currentConfig.browser);
    case "Update Instance Info":
      return await showInstanceInfoDialog(currentConfig);
    default:
      return undefined;
  }
}

/**
 * 显示端口配置对话框
 */
async function showPortConfigurationDialog(
  currentPorts: any
): Promise<{ ports: any } | undefined> {
  const httpPort = await vscode.window.showInputBox({
    prompt: "Enter HTTP port",
    value: currentPorts.httpPort.toString(),
    validateInput: (value) => {
      const port = parseInt(value);
      if (isNaN(port) || port < 1024 || port > 65535) {
        return "Port must be between 1024 and 65535";
      }
      return null;
    },
  });

  if (!httpPort) return undefined;

  const httpsPort = await vscode.window.showInputBox({
    prompt: "Enter HTTPS port",
    value: currentPorts.httpsPort.toString(),
    validateInput: (value) => {
      const port = parseInt(value);
      if (isNaN(port) || port < 1024 || port > 65535) {
        return "Port must be between 1024 and 65535";
      }
      return null;
    },
  });

  if (!httpsPort) return undefined;

  return {
    ports: {
      ...currentPorts,
      httpPort: parseInt(httpPort),
      httpsPort: parseInt(httpsPort),
    },
  };
}

/**
 * 显示JVM配置对话框
 */
async function showJvmConfigurationDialog(
  currentJvm: any
): Promise<{ jvm: any } | undefined> {
  const maxHeapSize = await vscode.window.showInputBox({
    prompt: "Enter maximum heap size",
    value: currentJvm.maxHeapSize,
    placeHolder: "e.g., 512m, 1g, 2048m",
  });

  if (!maxHeapSize) return undefined;

  return {
    jvm: {
      ...currentJvm,
      maxHeapSize,
    },
  };
}

/**
 * 显示浏览器配置对话框
 */
async function showBrowserConfigurationDialog(
  currentBrowser: any
): Promise<{ browser: any } | undefined> {
  const autoOpen = await vscode.window.showQuickPick(["Yes", "No"], {
    placeHolder: "Auto-open browser when starting Tomcat?",
  });

  if (!autoOpen) return undefined;

  return {
    browser: {
      ...currentBrowser,
      autoOpen: autoOpen === "Yes",
    },
  };
}

/**
 * 显示实例信息对话框
 */
async function showInstanceInfoDialog(
  currentConfig: TomcatInstanceConfiguration
): Promise<{ name?: string; description?: string } | undefined> {
  const name = await vscode.window.showInputBox({
    prompt: "Enter instance name",
    value: currentConfig.name,
    validateInput: (value) => {
      if (!value || !value.trim()) {
        return "Instance name is required";
      }
      return null;
    },
  });

  if (!name) return undefined;

  const description = await vscode.window.showInputBox({
    prompt: "Enter instance description",
    value: currentConfig.description || "",
  });

  return {
    name: name.trim(),
    description: description?.trim() || "",
  };
}

/**
 * 扩展停用函数
 */
export function deactivate() {
  console.log("Tomcat Manager extension is now deactivated");

  // 停止所有热部署监听
  try {
    const { HotDeployService } = require("./services/HotDeployService");
    const hotDeployService = HotDeployService.getInstance();
    hotDeployService.stopAllWatching();
    console.log("All hot deploy watchers stopped");
  } catch (error) {
    console.error("Error stopping hot deploy watchers:", error);
  }
}
