import * as net from "net";
import { PortConfiguration } from "./TomcatInstance";

/**
 * 端口状态枚举
 */
export enum PortStatus {
  AVAILABLE = "available",
  IN_USE = "in_use",
  RESERVED = "reserved",
}

/**
 * 端口信息接口
 */
export interface PortInfo {
  port: number;
  status: PortStatus;
  usedBy?: string; // 使用该端口的实例ID或进程名
}

/**
 * 端口范围配置
 */
export interface PortRangeConfiguration {
  httpStart: number;
  httpsStart: number;
  ajpStart: number;
  jmxStart: number;
  shutdownStart: number;
  debugStart: number;
}

/**
 * 端口管理器类
 */
export class PortManager {
  private static instance: PortManager;
  private reservedPorts: Map<number, string> = new Map(); // port -> instanceId
  private portRanges: PortRangeConfiguration;

  private constructor() {
    this.portRanges = {
      httpStart: 8080,
      httpsStart: 8443,
      ajpStart: 8009,
      jmxStart: 9999,
      shutdownStart: 8005,
      debugStart: 5005,
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PortManager {
    if (!PortManager.instance) {
      PortManager.instance = new PortManager();
    }
    return PortManager.instance;
  }

  /**
   * 设置端口范围配置
   */
  setPortRanges(ranges: PortRangeConfiguration): void {
    this.portRanges = { ...ranges };
  }

  /**
   * 获取端口范围配置
   */
  getPortRanges(): PortRangeConfiguration {
    return { ...this.portRanges };
  }

  /**
   * 检查端口是否可用
   */
  async isPortAvailable(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const server = net.createServer();

      server.listen(port, () => {
        server.once("close", () => {
          resolve(true);
        });
        server.close();
      });

      server.on("error", () => {
        resolve(false);
      });
    });
  }

  /**
   * 获取端口状态
   */
  async getPortStatus(port: number): Promise<PortInfo> {
    const isAvailable = await this.isPortAvailable(port);
    const reservedBy = this.reservedPorts.get(port);

    if (reservedBy) {
      return {
        port,
        status: PortStatus.RESERVED,
        usedBy: reservedBy,
      };
    }

    return {
      port,
      status: isAvailable ? PortStatus.AVAILABLE : PortStatus.IN_USE,
    };
  }

  /**
   * 预留端口
   */
  reservePort(port: number, instanceId: string): boolean {
    if (this.reservedPorts.has(port)) {
      return false;
    }
    this.reservedPorts.set(port, instanceId);
    return true;
  }

  /**
   * 释放端口
   */
  releasePort(port: number): void {
    this.reservedPorts.delete(port);
  }

  /**
   * 释放实例的所有端口
   */
  releaseInstancePorts(instanceId: string): void {
    for (const [port, id] of this.reservedPorts.entries()) {
      if (id === instanceId) {
        this.reservedPorts.delete(port);
      }
    }
  }

  /**
   * 预留实例的所有端口
   */
  async reserveInstancePorts(
    ports: PortConfiguration,
    instanceId: string
  ): Promise<boolean> {
    const allPorts = [
      ports.httpPort,
      ports.httpsPort,
      ports.ajpPort,
      ports.jmxPort,
      ports.shutdownPort,
    ];

    // 检查所有端口是否可用
    for (const port of allPorts) {
      const status = await this.getPortStatus(port);
      if (status.status !== PortStatus.AVAILABLE) {
        return false;
      }
    }

    // 预留所有端口
    for (const port of allPorts) {
      this.reservePort(port, instanceId);
    }

    return true;
  }

  /**
   * 查找下一个可用端口
   */
  async findNextAvailablePort(
    startPort: number,
    maxAttempts: number = 100
  ): Promise<number | null> {
    for (let i = 0; i < maxAttempts; i++) {
      const port = startPort + i;
      if (port > 65535) {
        break;
      }

      const status = await this.getPortStatus(port);
      if (status.status === PortStatus.AVAILABLE) {
        return port;
      }
    }
    return null;
  }

  /**
   * 生成可用的端口配置
   */
  async generateAvailablePortConfiguration(): Promise<PortConfiguration | null> {
    const httpPort = await this.findNextAvailablePort(
      this.portRanges.httpStart
    );
    if (!httpPort) return null;

    const httpsPort = await this.findNextAvailablePort(
      this.portRanges.httpsStart
    );
    if (!httpsPort) return null;

    const ajpPort = await this.findNextAvailablePort(this.portRanges.ajpStart);
    if (!ajpPort) return null;

    const jmxPort = await this.findNextAvailablePort(this.portRanges.jmxStart);
    if (!jmxPort) return null;

    const shutdownPort = await this.findNextAvailablePort(
      this.portRanges.shutdownStart
    );
    if (!shutdownPort) return null;

    const debugPort = await this.findNextAvailablePort(
      this.portRanges.debugStart
    );
    if (!debugPort) return null;

    return {
      httpPort,
      httpsPort,
      ajpPort,
      jmxPort,
      shutdownPort,
      debugPort,
    };
  }

  /**
   * 验证端口配置
   */
  async validatePortConfiguration(
    ports: PortConfiguration,
    excludeInstanceId?: string
  ): Promise<string[]> {
    const errors: string[] = [];
    const allPorts = [
      { name: "HTTP", port: ports.httpPort },
      { name: "HTTPS", port: ports.httpsPort },
      { name: "AJP", port: ports.ajpPort },
      { name: "JMX", port: ports.jmxPort },
      { name: "Shutdown", port: ports.shutdownPort },
      { name: "Debug", port: ports.debugPort },
    ];

    // 检查端口范围
    for (const { name, port } of allPorts) {
      if (port < 1024 || port > 65535) {
        errors.push(`${name} port ${port} is out of valid range (1024-65535)`);
      }
    }

    // 检查端口重复
    const portNumbers = allPorts.map((p) => p.port);
    const uniquePorts = new Set(portNumbers);
    if (uniquePorts.size !== portNumbers.length) {
      errors.push("Duplicate ports detected in configuration");
    }

    // 检查端口可用性
    for (const { name, port } of allPorts) {
      const status = await this.getPortStatus(port);
      if (status.status === PortStatus.IN_USE) {
        errors.push(`${name} port ${port} is already in use`);
      } else if (
        status.status === PortStatus.RESERVED &&
        status.usedBy !== excludeInstanceId
      ) {
        errors.push(`${name} port ${port} is reserved by another instance`);
      }
    }

    return errors;
  }

  /**
   * 获取所有预留的端口信息
   */
  getReservedPorts(): Map<number, string> {
    return new Map(this.reservedPorts);
  }

  /**
   * 获取实例使用的端口
   */
  getInstancePorts(instanceId: string): number[] {
    const ports: number[] = [];
    for (const [port, id] of this.reservedPorts.entries()) {
      if (id === instanceId) {
        ports.push(port);
      }
    }
    return ports.sort((a, b) => a - b);
  }

  /**
   * 检查端口冲突
   */
  async checkPortConflicts(): Promise<{ port: number; conflicts: string[] }[]> {
    const conflicts: { port: number; conflicts: string[] }[] = [];

    for (const [port, instanceId] of this.reservedPorts.entries()) {
      const isAvailable = await this.isPortAvailable(port);
      if (!isAvailable) {
        conflicts.push({
          port,
          conflicts: [
            `Port ${port} reserved by ${instanceId} but is in use by another process`,
          ],
        });
      }
    }

    return conflicts;
  }

  /**
   * 清理无效的端口预留
   */
  async cleanupInvalidReservations(): Promise<void> {
    const toRemove: number[] = [];

    for (const [port] of this.reservedPorts.entries()) {
      const isAvailable = await this.isPortAvailable(port);
      if (isAvailable) {
        // 端口可用，但被预留，可能是实例已停止但未正确释放
        // 这里可以添加更复杂的逻辑来判断是否需要清理
      }
    }

    for (const port of toRemove) {
      this.reservedPorts.delete(port);
    }
  }
}
